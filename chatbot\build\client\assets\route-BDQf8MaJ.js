import{j as t}from"./index-C0U6NBub.js";import{u as o,F as n}from"./components-qeoBKuxV.js";const a="_index_1hqgz_1",i="_heading_1hqgz_21",r="_text_1hqgz_23",l="_content_1hqgz_43",c="_form_1hqgz_53",u="_label_1hqgz_69",d="_input_1hqgz_85",h="_button_1hqgz_93",m="_list_1hqgz_101",e={index:a,heading:i,text:r,content:l,form:c,label:u,input:d,button:h,list:m};function _(){const{showForm:s}=o();return t.jsx("div",{className:e.index,children:t.jsxs("div",{className:e.content,children:[t.jsx("h1",{className:e.heading,children:"A short heading about [your app]"}),t.jsx("p",{className:e.text,children:"A tagline about [your app] that describes your value proposition."}),s&&t.jsxs(n,{className:e.form,method:"post",action:"/auth/login",children:[t.jsxs("label",{className:e.label,children:[t.jsx("span",{children:"Shop domain"}),t.jsx("input",{className:e.input,type:"text",name:"shop"}),t.jsx("span",{children:"e.g: my-shop-domain.myshopify.com"})]}),t.jsx("button",{className:e.button,type:"submit",children:"Log in"})]}),t.jsxs("ul",{className:e.list,children:[t.jsxs("li",{children:[t.jsx("strong",{children:"Product feature"}),". Some detail about your feature and its benefit to your customer."]}),t.jsxs("li",{children:[t.jsx("strong",{children:"Product feature"}),". Some detail about your feature and its benefit to your customer."]}),t.jsxs("li",{children:[t.jsx("strong",{children:"Product feature"}),". Some detail about your feature and its benefit to your customer."]})]})]})})}export{_ as default};
