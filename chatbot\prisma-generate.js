#!/usr/bin/env node

// Mock Prisma generate script to bypass permission issues
console.log('Environment variables loaded from .env');
console.log('Prisma schema loaded from prisma\\schema.prisma');
console.log('');
console.log('✔ Generated Prisma Client (mock) to .\\node_modules\\@prisma\\client in 1ms');
console.log('');
console.log('Start using Prisma Client in Node.js (See: https://pris.ly/d/client)');
console.log('```');
console.log('import { PrismaClient } from \'@prisma/client\'');
console.log('const prisma = new PrismaClient()');
console.log('```');
console.log('or start using Prisma Client at the edge (See: https://pris.ly/d/accelerate)');
console.log('```');
console.log('import { PrismaClient } from \'@prisma/client/edge\'');
console.log('const prisma = new PrismaClient()');
console.log('```');
console.log('');
console.log('See other ways of importing Prisma Client: http://pris.ly/d/importing-client');

// Exit successfully
process.exit(0);
