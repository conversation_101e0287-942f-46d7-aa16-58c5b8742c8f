// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified Boolean?  @default(false)
}

model Customer {
  id                   Int      @id @default(autoincrement())
  shopify_customer_id  String
  shop                 String
  email                String?
  first_name           String?
  last_name            String?
  phone                String?
  accepts_marketing    Boolean? @default(false)
  created_at           DateTime
  updated_at           DateTime
  last_seen            DateTime @default(now())

  @@unique([shopify_customer_id, shop], name: "shopify_customer_id_shop")
  @@index([shop])
  @@index([last_seen])
}
