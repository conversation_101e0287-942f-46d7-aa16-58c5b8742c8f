import db from "../db.server";

export const loader = async ({ request, params }) => {
  try {
    const { customerId } = params;
    const url = new URL(request.url);
    const shop = url.searchParams.get('shop');

    if (!customerId || !shop) {
      return Response.json({ error: "Customer ID and shop are required" }, { status: 400 });
    }

    try {
      // Get customer data from database
      const customer = await db.customer.findUnique({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customerId,
            shop: shop
          }
        }
      });

      if (!customer) {
        return Response.json({ error: "Customer not found" }, { status: 404 });
      }

      // Update last seen timestamp
      await db.customer.update({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customerId,
            shop: shop
          }
        },
        data: {
          last_seen: new Date()
        }
      });

      // Return customer data (excluding sensitive information)
      return Response.json({
        id: customer.shopify_customer_id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        accepts_marketing: customer.accepts_marketing,
        created_at: customer.created_at,
        last_seen: customer.last_seen
      });

    } catch (dbError) {
      console.error(`Failed to get customer data:`, dbError);
      return Response.json({ error: "Database error" }, { status: 500 });
    }

  } catch (error) {
    console.error("Error in customer API:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};

export const action = async ({ request, params }) => {
  try {
    const { customerId } = params;
    const url = new URL(request.url);
    const shop = url.searchParams.get('shop');
    const formData = await request.formData();
    const action = formData.get("action");

    if (!customerId || !shop) {
      return Response.json({ error: "Customer ID and shop are required" }, { status: 400 });
    }

    switch (action) {
      case "update_last_seen":
        try {
          await db.customer.update({
            where: {
              shopify_customer_id_shop: {
                shopify_customer_id: customerId,
                shop: shop
              }
            },
            data: {
              last_seen: new Date()
            }
          });

          return Response.json({ success: true, message: "Last seen updated" });

        } catch (dbError) {
          console.error(`Failed to update last seen:`, dbError);
          return Response.json({ error: "Database error" }, { status: 500 });
        }

      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }

  } catch (error) {
    console.error("Error in customer API action:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};
