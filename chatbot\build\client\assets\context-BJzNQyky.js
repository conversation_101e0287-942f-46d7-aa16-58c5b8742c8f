import{g as Ae,r as S}from"./index-C0U6NBub.js";const He={props:{"data-polaris-scrollable":!0},selector:"[data-polaris-scrollable]"},kr={props:{"data-polaris-overlay":!0}},zr={props:{"data-polaris-layer":!0},selector:"[data-polaris-layer]"},Sr={props:{"data-polaris-unstyled":!0}},Oe={selector:"[data-polaris-top-bar]"},Ir={selector:"[data-portal-id]"};var Tr=["xs","sm","md","lg","xl"],Le={"breakpoints-xs":{value:"0px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-sm":{value:"490px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-md":{value:"768px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-lg":{value:"1040px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-xl":{value:"1440px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."}};function je(r,o){var a=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(a!=null){var n,l,g,d,b=[],p=!0,x=!1;try{if(g=(a=a.call(r)).next,o!==0)for(;!(p=(n=g.call(a)).done)&&(b.push(n.value),b.length!==o);p=!0);}catch(B){x=!0,l=B}finally{try{if(!p&&a.return!=null&&(d=a.return(),Object(d)!==d))return}finally{if(x)throw l}}return b}}function Pe(r,o){return o||(o=r.slice(0)),r.raw=o,r}function X(r,o){return _e(r)||je(r,o)||De(r,o)||Ye()}function _e(r){if(Array.isArray(r))return r}function De(r,o){if(r){if(typeof r=="string")return ie(r,o);var a=Object.prototype.toString.call(r).slice(8,-1);if(a==="Object"&&r.constructor&&(a=r.constructor.name),a==="Map"||a==="Set")return Array.from(r);if(a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return ie(r,o)}}function ie(r,o){(o==null||o>r.length)&&(o=r.length);for(var a=0,n=new Array(o);a<o;a++)n[a]=r[a];return n}function Ye(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var ne,Q=16,P="px",L="em",j="rem",ue=new RegExp(String.raw(ne||(ne=Pe(["-?d+(?:.d+|d*)"],["-?\\d+(?:\\.\\d+|\\d*)"])))),We=new RegExp(P+"|"+L+"|"+j);function re(r){r===void 0&&(r="");var o=r.match(new RegExp(ue.source+"("+We.source+")"));return o&&o[1]}function $e(r){r===void 0&&(r="");var o=re(r);if(!o||o===P)return r;if(o===L||o===j)return""+parseFloat(r)*Q+P}function he(r,o){r===void 0&&(r=""),o===void 0&&(o=Q);var a=re(r);if(!a||a===L)return r;if(a===P)return""+parseFloat(r)/o+L;if(a===j)return""+parseFloat(r)*Q/o+L}function Ge(r){r===void 0&&(r="");var o=re(r);if(!o||o===j)return r;if(o===L)return""+parseFloat(r)+j;if(o===P)return""+parseFloat(r)/Q+j}function qe(r){return r.replace(new RegExp(ue.source+"("+P+")","g"),function(o){var a;return(a=Ge(o))!=null?a:o})}function pe(r){return Object.fromEntries(Object.entries(r).map(function(o){var a=X(o,2),n=a[0],l=a[1];return[n,Object.assign(Object.assign({},l),{},{value:qe(l.value)})]}))}function ve(r){return"--p-"+r}function i(r){return"var("+ve(r)+")"}function Qe(r){return Object.values(r).flatMap(function(o){return Object.keys(o)})}function Ve(r){var o=Object.entries(r),a=o.length-1;return Object.fromEntries(o.map(function(n,l){var g=n,d=X(g,2),b=d[0],p=d[1],x=Xe(p),B=le(p),M=l===a?x:x+" and "+le(o[l+1][1]);return[b,{up:x,down:B,only:M}]}))}function Xe(r){return"(min-width: "+he(r)+")"}function le(r){var o,a=parseFloat((o=$e(r))!=null?o:"")-.04;return"(max-width: "+he(a+"px")+")"}var xe=["border","breakpoints","font","height","shadow","space","text","width"];function Ke(r){return Object.fromEntries(Object.entries(r).map(function(o){var a=X(o,2),n=a[0],l=a[1];return[n,xe.includes(n)?pe(l):l]}))}var Z,ce;function Ze(){if(ce)return Z;ce=1;var r=function(s){return o(s)&&!a(s)};function o(c){return!!c&&typeof c=="object"}function a(c){var s=Object.prototype.toString.call(c);return s==="[object RegExp]"||s==="[object Date]"||g(c)}var n=typeof Symbol=="function"&&Symbol.for,l=n?Symbol.for("react.element"):60103;function g(c){return c.$$typeof===l}function d(c){return Array.isArray(c)?[]:{}}function b(c,s){return s.clone!==!1&&s.isMergeableObject(c)?I(d(c),c,s):c}function p(c,s,m){return c.concat(s).map(function(T){return b(T,m)})}function x(c,s){if(!s.customMerge)return I;var m=s.customMerge(c);return typeof m=="function"?m:I}function B(c){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(c).filter(function(s){return Object.propertyIsEnumerable.call(c,s)}):[]}function M(c){return Object.keys(c).concat(B(c))}function A(c,s){try{return s in c}catch{return!1}}function O(c,s){return A(c,s)&&!(Object.hasOwnProperty.call(c,s)&&Object.propertyIsEnumerable.call(c,s))}function R(c,s,m){var T={};return m.isMergeableObject(c)&&M(c).forEach(function(h){T[h]=b(c[h],m)}),M(s).forEach(function(h){O(c,h)||(A(c,h)&&m.isMergeableObject(s[h])?T[h]=x(h,m)(c[h],s[h],m):T[h]=b(s[h],m))}),T}function I(c,s,m){m=m||{},m.arrayMerge=m.arrayMerge||p,m.isMergeableObject=m.isMergeableObject||r,m.cloneUnlessOtherwiseSpecified=b;var T=Array.isArray(s),h=Array.isArray(c),Y=T===h;return Y?T?m.arrayMerge(c,s,m):R(c,s,m):b(s,m)}I.all=function(s,m){if(!Array.isArray(s))throw new Error("first argument should be an array");return s.reduce(function(T,h){return I(T,h,m)},{})};var C=I;return Z=C,Z}var Je=Ze();const er=Ae(Je);var t={0:"0px","0165":"0.66px","025":"1px","050":"2px",100:"4px",150:"6px",200:"8px",275:"11px",300:"12px",325:"13px",350:"14px",400:"16px",450:"18px",500:"20px",550:"22px",600:"24px",700:"28px",750:"30px",800:"32px",900:"36px",1e3:"40px",1200:"48px",1600:"64px",2e3:"80px",2400:"96px",2800:"112px",3200:"128px"},rr={"border-radius-0":{value:t[0]},"border-radius-050":{value:t["050"]},"border-radius-100":{value:t[100]},"border-radius-150":{value:t[150]},"border-radius-200":{value:t[200]},"border-radius-300":{value:t[300]},"border-radius-400":{value:t[400]},"border-radius-500":{value:t[500]},"border-radius-750":{value:t[750]},"border-radius-full":{value:"9999px"},"border-width-0":{value:t[0]},"border-width-0165":{value:t["0165"]},"border-width-025":{value:t["025"]},"border-width-050":{value:t["050"]},"border-width-100":{value:t[100]}},e={1:"rgba(255, 255, 255, 1)",2:"rgba(253, 253, 253, 1)",3:"rgba(250, 250, 250, 1)",4:"rgba(247, 247, 247, 1)",5:"rgba(243, 243, 243, 1)",6:"rgba(241, 241, 241, 1)",7:"rgba(235, 235, 235, 1)",8:"rgba(227, 227, 227, 1)",9:"rgba(212, 212, 212, 1)",10:"rgba(204, 204, 204, 1)",11:"rgba(181, 181, 181, 1)",12:"rgba(138, 138, 138, 1)",13:"rgba(97, 97, 97, 1)",14:"rgba(74, 74, 74, 1)",15:"rgba(48, 48, 48, 1)",16:"rgba(26, 26, 26, 1)"},y={3:"rgba(234, 244, 255, 1)",4:"rgba(224, 240, 255, 1)",5:"rgba(213, 235, 255, 1)",6:"rgba(202, 230, 255, 1)",8:"rgba(168, 216, 255, 1)",9:"rgba(145, 208, 255, 1)",10:"rgba(81, 192, 255, 1)",11:"rgba(0, 148, 213, 1)",12:"rgba(0, 124, 180, 1)",15:"rgba(0, 58, 90, 1)",16:"rgba(0, 33, 51, 1)"},u={1:"rgba(252, 253, 255, 1)",3:"rgba(240, 242, 255, 1)",4:"rgba(234, 237, 255, 1)",5:"rgba(226, 231, 255, 1)",7:"rgba(213, 220, 255, 1)",8:"rgba(197, 208, 255, 1)",13:"rgba(0, 91, 211, 1)",14:"rgba(0, 66, 153, 1)",15:"rgba(0, 46, 106, 1)"},w={1:"rgba(248, 255, 251, 1)",3:"rgba(205, 254, 225, 1)",4:"rgba(180, 254, 210, 1)",5:"rgba(146, 254, 194, 1)",7:"rgba(56, 250, 163, 1)",12:"rgba(41, 132, 90, 1)",13:"rgba(19, 111, 69, 1)",14:"rgba(12, 81, 50, 1)",15:"rgba(8, 61, 37, 1)",16:"rgba(9, 42, 27, 1)"},se={9:"rgba(37, 232, 43, 1)",15:"rgba(3, 61, 5, 1)"},ge={3:"rgba(253, 239, 253, 1)",12:"rgba(197, 48, 197, 1)"},U={3:"rgba(255, 241, 227, 1)",4:"rgba(255, 235, 213, 1)",5:"rgba(255, 228, 198, 1)",7:"rgba(255, 214, 164, 1)",8:"rgba(255, 200, 121, 1)",9:"rgba(255, 184, 0, 1)",10:"rgba(229, 165, 0, 1)",11:"rgba(178, 132, 0, 1)",12:"rgba(149, 111, 0, 1)",14:"rgba(94, 66, 0, 1)",15:"rgba(65, 45, 0, 1)",16:"rgba(37, 26, 0, 1)"},k={1:"rgba(253, 253, 255, 1)",2:"rgba(248, 247, 255, 1)",3:"rgba(243, 241, 255, 1)",5:"rgba(233, 229, 255, 1)",6:"rgba(228, 222, 255, 1)",7:"rgba(223, 217, 255, 1)",11:"rgba(148, 116, 255, 1)",12:"rgba(128, 81, 255, 1)",13:"rgba(113, 38, 255, 1)",14:"rgba(87, 0, 209, 1)"},z={1:"rgba(255, 251, 251, 1)",4:"rgba(254, 233, 232, 1)",5:"rgba(254, 226, 225, 1)",6:"rgba(254, 218, 217, 1)",7:"rgba(254, 211, 209, 1)",8:"rgba(254, 195, 193, 1)",11:"rgba(239, 77, 47, 1)",12:"rgba(229, 28, 0, 1)",13:"rgba(181, 38, 11, 1)",14:"rgba(142, 31, 11, 1)",15:"rgba(95, 21, 7, 1)",16:"rgba(47, 10, 4, 1)"},be={2:"rgba(255, 246, 248, 1)",11:"rgba(253, 75, 146, 1)"},me={9:"rgba(44, 224, 212, 1)",15:"rgba(3, 60, 57, 1)"},F={2:"rgba(255, 248, 219, 1)",3:"rgba(255, 244, 191, 1)",4:"rgba(255, 239, 157, 1)",5:"rgba(255, 235, 120, 1)",6:"rgba(255, 230, 0, 1)",8:"rgba(234, 211, 0, 1)",9:"rgba(225, 203, 0, 1)",11:"rgba(153, 138, 0, 1)",12:"rgba(130, 117, 0, 1)",14:"rgba(79, 71, 0, 1)",15:"rgba(51, 46, 0, 1)",16:"rgba(31, 28, 0, 1)"},v={1:"rgba(0, 0, 0, 0)",3:"rgba(0, 0, 0, 0.02)",5:"rgba(0, 0, 0, 0.05)",6:"rgba(0, 0, 0, 0.06)",7:"rgba(0, 0, 0, 0.08)",8:"rgba(0, 0, 0, 0.11)",9:"rgba(0, 0, 0, 0.17)",10:"rgba(0, 0, 0, 0.20)",14:"rgba(0, 0, 0, 0.71)",15:"rgba(0, 0, 0, 0.81)"},N={4:"rgba(255, 255, 255, 0.03)",8:"rgba(255, 255, 255, 0.11)",9:"rgba(255, 255, 255, 0.17)",10:"rgba(255, 255, 255, 0.20)",11:"rgba(255, 255, 255, 0.28)"},or={"color-scheme":{value:"light"},"color-bg":{value:e[6],description:"The default background color of the admin."},"color-bg-inverse":{value:e[16],description:"Use for high contrast page or component backgrounds."},"color-bg-surface":{value:e[1],description:"The background color for elements with the highest level of prominence, like a card."},"color-bg-surface-hover":{value:e[4],description:"The hover state color for elements with the highest level of prominence."},"color-bg-surface-active":{value:e[5],description:"The active state (on press) color for elements with the highest level of prominence."},"color-bg-surface-selected":{value:e[6],description:"The selected state color for elements with the highest level of prominence."},"color-bg-surface-disabled":{value:v[5],description:"The disabled state color for elements."},"color-bg-surface-secondary":{value:e[4],description:"The background color for elements with a secondary level of prominence."},"color-bg-surface-secondary-hover":{value:e[6],description:"The hover state color for elements with a secondary level of prominence."},"color-bg-surface-secondary-active":{value:e[7],description:"The active state (on press) color for elements with a secondary level of prominence."},"color-bg-surface-secondary-selected":{value:e[7],description:"The selected state color for elements with a secondary level of prominence."},"color-bg-surface-tertiary":{value:e[5],description:"The background color for elements with a third level of prominence."},"color-bg-surface-tertiary-hover":{value:e[7],description:"The hover state color for elements with a third level of prominence."},"color-bg-surface-tertiary-active":{value:e[8],description:"The active state (on press) color for elements with a third level of prominence."},"color-bg-surface-brand":{value:e[8],description:"Use to apply the key color to elements."},"color-bg-surface-brand-hover":{value:e[7],description:"The hover state color for key elements."},"color-bg-surface-brand-active":{value:e[6],description:"The active state (on press) color for key elements."},"color-bg-surface-brand-selected":{value:e[6],description:"The selected state color for key elements."},"color-bg-surface-info":{value:y[3],description:"Use for backgrounds communicating important information, like banners."},"color-bg-surface-info-hover":{value:y[4],description:"The hover state color for communicating important information."},"color-bg-surface-info-active":{value:y[6],description:"The active state (on press) color for communicating important information."},"color-bg-surface-success":{value:w[3],description:"Use for backgrounds communicating success, like banners."},"color-bg-surface-success-hover":{value:w[4],description:"The hover state color for communicating success."},"color-bg-surface-success-active":{value:w[5],description:"The active state (on press) color for communicating success."},"color-bg-surface-caution":{value:F[2],description:"Use for backgrounds communicating caution, like banners."},"color-bg-surface-caution-hover":{value:F[3],description:"The hover state for communicating caution."},"color-bg-surface-caution-active":{value:F[4],description:"The active state (on press) color for communicating caution."},"color-bg-surface-warning":{value:U[3],description:"Use for backgrounds communicating warning, like banners."},"color-bg-surface-warning-hover":{value:U[4],description:"The hover state color for communicating warning."},"color-bg-surface-warning-active":{value:U[5],description:"The active state (on press) color for communicating warning."},"color-bg-surface-critical":{value:z[4],description:"Use for backgrounds communicating critical information, like banners or input errors."},"color-bg-surface-critical-hover":{value:z[5],description:"The hover state color for communicating critical information."},"color-bg-surface-critical-active":{value:z[6],description:"The active state (on press) color for communicating critical information."},"color-bg-surface-emphasis":{value:u[3],description:"Use for backgrounds indicating areas of focus in editors, such as the theme editor."},"color-bg-surface-emphasis-hover":{value:u[4],description:"The hover state color for elements indicating areas of focus in editors."},"color-bg-surface-emphasis-active":{value:u[5],description:"The active state (on press) color for elements indicating areas of focus in editors."},"color-bg-surface-magic":{value:k[2],description:"Use for backgrounds of elements suggested by magic AI."},"color-bg-surface-magic-hover":{value:k[3],description:"The hover state color for elements suggested by magic AI."},"color-bg-surface-magic-active":{value:k[5],description:"The active state (on press) color for elements suggested by magic AI."},"color-bg-surface-inverse":{value:e[15],description:"Use for elements on bg-inverse."},"color-bg-surface-transparent":{value:v[1],description:"Use for elements that need a fully transparent background."},"color-bg-fill":{value:e[1],description:"The background color of contained elements with a smaller surface area, like a button."},"color-bg-fill-hover":{value:e[3],description:"The hover state color of contained elements with a smaller surface area, like a button."},"color-bg-fill-active":{value:e[4],description:"The active state (on press) color of contained elements with a smaller surface area, like a button."},"color-bg-fill-selected":{value:e[10],description:"The selected state color of contained elements with a smaller surface area, like a button or checkbox."},"color-bg-fill-disabled":{value:v[5],description:"The disabled state color of contained elements with a smaller surface area, like a button."},"color-bg-fill-secondary":{value:e[6],description:"The background color of elements with a smaller surface area and a secondary level of prominence."},"color-bg-fill-secondary-hover":{value:e[7],description:"The hover state color of elements with a smaller surface area and a secondary level of prominence."},"color-bg-fill-secondary-active":{value:e[8],description:"The active state (on press) color of elements with a smaller surface area and a secondary level of prominence."},"color-bg-fill-tertiary":{value:e[8],description:"The background color of elements with a smaller surface area and a third level of prominence."},"color-bg-fill-tertiary-hover":{value:e[9],description:"The hover state color of elements with a smaller surface area and a third level of prominence."},"color-bg-fill-tertiary-active":{value:e[10],description:"The active state (on press) color of elements with a smaller surface area and a third level of prominence."},"color-bg-fill-brand":{value:e[15],description:"The background color of main actions, like primary buttons."},"color-bg-fill-brand-hover":{value:e[16],description:"The hover state color of main actions, like primary buttons."},"color-bg-fill-brand-active":{value:e[16],description:"The active state (on press) color of main actions, like primary buttons."},"color-bg-fill-brand-selected":{value:e[15],description:"The selected state color of main actions, like primary buttons."},"color-bg-fill-brand-disabled":{value:v[9],description:"The disabled state color of main actions, like primary buttons."},"color-bg-fill-info":{value:y[9],description:"Use for backgrounds communicating important information on elements with a smaller surface area, like a badge or button."},"color-bg-fill-info-hover":{value:y[10],description:"The hover state color for communicating important information on elements with a smaller surface area."},"color-bg-fill-info-active":{value:y[11],description:"The active state (on press) color for communicating important information on elements with a smaller surface area."},"color-bg-fill-info-secondary":{value:y[5],description:"Use for backgrounds communicating important information on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-success":{value:w[12],description:"Use for backgrounds communicating success on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-success-hover":{value:w[13],description:"The hover state color for communicating success on elements with a smaller surface area."},"color-bg-fill-success-active":{value:w[14],description:"The active state (on press) color for communicating success on elements with a smaller surface area."},"color-bg-fill-success-secondary":{value:w[4],description:"Use for backgrounds communicating success on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-warning":{value:U[9],description:"Use for backgrounds communicating warning on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-warning-hover":{value:U[10],description:"The hover state color for communicating warning on elements with a smaller surface area."},"color-bg-fill-warning-active":{value:U[11],description:"The active state (on press) color for communicating warning on elements with a smaller surface area."},"color-bg-fill-warning-secondary":{value:U[7],description:"Use for backgrounds communicating warning on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-caution":{value:F[6],description:"Use for backgrounds communicating caution on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-caution-hover":{value:F[8],description:"The hover state color for communicating caution on elements with a smaller surface area."},"color-bg-fill-caution-active":{value:F[9],description:"The active state (on press) color for communicating caution on elements with a smaller surface area."},"color-bg-fill-caution-secondary":{value:F[5],description:"Use for backgrounds communicating caution on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-critical":{value:z[12],description:"Use for backgrounds communicating critical information on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-critical-hover":{value:z[13],description:"The hover state color for communicating critical information on elements with a smaller surface area."},"color-bg-fill-critical-active":{value:z[14],description:"The active state (on press) color for communicating critical information on elements with a smaller surface area."},"color-bg-fill-critical-selected":{value:z[14],description:"The selected state color for communicating critical information on elements with a smaller surface area."},"color-bg-fill-critical-secondary":{value:z[7],description:"Use for backgrounds communicating critical information on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-emphasis":{value:u[13],description:"Use for backgrounds indicating areas of focus in editors on elements with a smaller surface area, like a button or a badge."},"color-bg-fill-emphasis-hover":{value:u[14],description:"The hover state color for indicating areas of focus in editors on elements with a smaller surface area."},"color-bg-fill-emphasis-active":{value:u[15],description:"The active state (on press) color for indicating areas of focus in editors on elements with a smaller surface area."},"color-bg-fill-magic":{value:k[12],description:"The background color of elements suggested by magic AI, like a badge or a banner."},"color-bg-fill-magic-secondary":{value:k[5],description:"The background color of elements suggested by magic AI, with a secondary level of prominence."},"color-bg-fill-magic-secondary-hover":{value:k[6],description:"The hover state color of elements suggested by magic AI, with a secondary level of prominence."},"color-bg-fill-magic-secondary-active":{value:k[7],description:"The active state (on press) color of elements suggested by magic AI, with a secondary level of prominence."},"color-bg-fill-inverse":{value:e[15],description:"The background color of elements with a smaller surface area on an inverse background."},"color-bg-fill-inverse-hover":{value:e[14],description:"The hover state color of elements with a smaller surface area on an inverse background."},"color-bg-fill-inverse-active":{value:e[13],description:"The active state (on press) color of elements with a smaller surface area on an inverse background."},"color-bg-fill-transparent":{value:v[3],description:"The background color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-hover":{value:v[5],description:"The hover state color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-active":{value:v[7],description:"The active state (on press) color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-selected":{value:v[7],description:"The selected state color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-secondary":{value:v[6],description:"The background color of elements that need to sit on different background colors, with a secondary level of prominence."},"color-bg-fill-transparent-secondary-hover":{value:v[7],description:"The hover state color of elements that need to sit on different background colors, with a secondary level of prominence."},"color-bg-fill-transparent-secondary-active":{value:v[8],description:"The active state (on press) color of elements that need to sit on different background colors, with a secondary level of prominence."},"color-text":{value:e[15],description:"The default text color."},"color-text-secondary":{value:e[13],description:"Use for text with a secondary level of prominence."},"color-text-disabled":{value:e[11],description:"Use for text in a disabled state."},"color-text-link":{value:u[13],description:"Use for text links."},"color-text-link-hover":{value:u[14],description:"The hover state color for text links."},"color-text-link-active":{value:u[15],description:"The active state (on press) color for text links."},"color-text-brand":{value:e[14],description:"Use for text that needs to pull attention."},"color-text-brand-hover":{value:e[15],description:"The hover state color for text that needs to pull attention."},"color-text-brand-on-bg-fill":{value:e[1],description:"Use for text on bg-fill-brand, like primary buttons."},"color-text-brand-on-bg-fill-hover":{value:e[8],description:"The hover state color for text on bg-fill-brand-hover."},"color-text-brand-on-bg-fill-active":{value:e[10],description:"The active state (on press) color for text on bg-fill-brand."},"color-text-brand-on-bg-fill-disabled":{value:e[1],description:"The disabled state color for text on bg-fill-brand-disabled."},"color-text-info":{value:y[15],description:"Use for text communicating important information."},"color-text-info-hover":{value:y[15],description:"The hover state color for text communicating important information."},"color-text-info-active":{value:y[16],description:"The active state (on press) color for text communicating important information."},"color-text-info-secondary":{value:y[12],description:"Use for text communicating important information with a secondary level of prominence."},"color-text-info-on-bg-fill":{value:y[16],description:"Use for text and icons on bg-fill-info."},"color-text-success":{value:w[14],description:"Use for text communicating success."},"color-text-success-hover":{value:w[15],description:"The hover state color for text communicating success."},"color-text-success-active":{value:w[16],description:"The active state (on press) color for text communicating success."},"color-text-success-secondary":{value:w[12],description:"Use for text communicating success with a secondary level of prominence."},"color-text-success-on-bg-fill":{value:w[1],description:"Use for text and icons on bg-fill-success."},"color-text-caution":{value:F[14],description:"Use for text communicating caution."},"color-text-caution-hover":{value:F[15],description:"The hover state color for text communicating caution."},"color-text-caution-active":{value:F[16],description:"The active state (on press) color for text communicating caution."},"color-text-caution-secondary":{value:F[12],description:"Use for text communicating caution with a secondary level of prominence."},"color-text-caution-on-bg-fill":{value:F[15],description:"Use for text and icons on bg-fill-caution."},"color-text-warning":{value:U[14],description:"Use for text communicating warning."},"color-text-warning-hover":{value:U[15],description:"The hover state color for text communicating warning."},"color-text-warning-active":{value:U[16],description:"The active state (on press) color for text communicating warning."},"color-text-warning-secondary":{value:U[12],description:"Use for text communicating warning with a secondary level of prominence."},"color-text-warning-on-bg-fill":{value:U[16],description:"Use for text and icons on bg-fill-warning."},"color-text-critical":{value:z[14],description:"Use for text communicating critical information."},"color-text-critical-hover":{value:z[15],description:"The hover state color for text communicating critical information."},"color-text-critical-active":{value:z[16],description:"The active state (on press) color for text communicating critical information."},"color-text-critical-secondary":{value:z[12],description:"Use for text communicating critical information with a secondary level of prominence."},"color-text-critical-on-bg-fill":{value:z[1],description:"Use for text and icons on bg-fill-critical."},"color-text-emphasis":{value:u[13],description:"Use for text indicating areas of focus in editors, like the theme editor."},"color-text-emphasis-hover":{value:u[14],description:"The hover state color for text indicating areas of focus."},"color-text-emphasis-active":{value:u[15],description:"The active state (on press) color for text indicating areas of focus."},"color-text-emphasis-on-bg-fill":{value:u[1],description:"Use for text and icons on bg-fill-emphasis."},"color-text-emphasis-on-bg-fill-hover":{value:u[5],description:"Use for text and icons on bg-fill-emphasis-hover."},"color-text-emphasis-on-bg-fill-active":{value:u[7],description:"Use for text and icons on bg-fill-emphasis-active."},"color-text-magic":{value:k[14],description:"Use for text suggested by magic AI."},"color-text-magic-secondary":{value:k[13],description:"Use for text suggested by magic AI with a secondary level of prominence."},"color-text-magic-on-bg-fill":{value:k[1],description:"Use for text and icons on bg-fill-magic."},"color-text-inverse":{value:e[8],description:"Use for text on an inverse background."},"color-text-inverse-secondary":{value:e[11],description:"Use for secondary text on an inverse background."},"color-text-link-inverse":{value:u[8],description:"Use for text links on an inverse background."},"color-border":{value:e[8],description:"The default color for borders on any element."},"color-border-hover":{value:e[10],description:"The hover color for borders on any element."},"color-border-disabled":{value:e[7],description:"The disabled color for borders on any element."},"color-border-secondary":{value:e[7],description:"The color for hr elements or any visual dividers."},"color-border-tertiary":{value:e[10],description:"The border color on any element. Pair with bg-surface-tertiary or bg-fill-tertiary."},"color-border-focus":{value:u[13],description:"The focus ring for any interactive element in a focused state."},"color-border-brand":{value:e[8],description:"Use for borders paired with brand colors."},"color-border-info":{value:y[8],description:"Use for borders communicating information."},"color-border-success":{value:w[5],description:"Use for borders communicating success."},"color-border-caution":{value:F[5],description:"Use for borders communicating caution."},"color-border-warning":{value:U[8],description:"Use for borders communicating warning."},"color-border-critical":{value:z[8],description:"Use for borders communicating critical information."},"color-border-critical-secondary":{value:z[14],description:"Use for borders communicating critical information, such as borders on invalid text fields."},"color-border-emphasis":{value:u[13],description:"Use for borders indicating areas of focus."},"color-border-emphasis-hover":{value:u[14],description:"The hover state color for borders indicating areas of focus."},"color-border-emphasis-active":{value:u[15],description:"The active state (on press) color for borders indicating areas of focus."},"color-border-magic":{value:k[6],description:"Use for borders suggested by magic AI."},"color-border-magic-secondary":{value:k[11],description:"Use for borders suggested by magic AI, such as borders on text fields."},"color-border-magic-secondary-hover":{value:k[12],description:"Use for borders suggested by magic AI, such as borders on text fields."},"color-border-inverse":{value:e[13],description:"Use for borders on an inverse background, such as borders on the global search."},"color-border-inverse-hover":{value:e[10],description:"The hover state color for borders on an inverse background."},"color-border-inverse-active":{value:e[8],description:"The active state (on press) color for borders on an inverse background."},"color-tooltip-tail-down-border-experimental":{value:e[9],description:"The border color for tooltip tails pointing down."},"color-tooltip-tail-up-border-experimental":{value:e[8],description:"The border color for tooltip tails pointing up."},"color-border-gradient-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-border-gradient-hover-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-border-gradient-selected-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-border-gradient-active-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-icon":{value:e[14],description:"The default color for icons."},"color-icon-hover":{value:e[15],description:"The hover state color for icons."},"color-icon-active":{value:e[16],description:"The active state (on press) color for icons."},"color-icon-disabled":{value:e[10],description:"The disabled state color for icons."},"color-icon-secondary":{value:e[12],description:"Use for secondary icons."},"color-icon-secondary-hover":{value:e[13],description:"The hover state color for secondary icons."},"color-icon-secondary-active":{value:e[14],description:"The active state (on press) color for secondary icons."},"color-icon-brand":{value:e[16],description:"Use for icons that need to pull more focus."},"color-icon-info":{value:y[11],description:"Use for icons communicating information."},"color-icon-success":{value:w[12],description:"Use for icons communicating success."},"color-icon-caution":{value:F[11],description:"Use for icons communicating caution."},"color-icon-warning":{value:U[11],description:"Use for icons communicating warning."},"color-icon-critical":{value:z[11],description:"Use for icons communicating critical information."},"color-icon-emphasis":{value:u[13],description:"Use for icons indicating areas of focus in editors, like the theme editor."},"color-icon-emphasis-hover":{value:u[14],description:"The hover color for icons indicating areas of focus in editors."},"color-icon-emphasis-active":{value:u[15],description:"The active state (on press) color for icons indicating areas of focus in editors."},"color-icon-magic":{value:k[12],description:"Use for icons suggested by magic AI."},"color-icon-inverse":{value:e[8],description:"Use for icons on an inverse background."},"color-avatar-bg-fill":{value:e[11]},"color-avatar-five-bg-fill":{value:be[11]},"color-avatar-five-text-on-bg-fill":{value:be[2]},"color-avatar-four-bg-fill":{value:y[10]},"color-avatar-four-text-on-bg-fill":{value:y[16]},"color-avatar-one-bg-fill":{value:ge[12]},"color-avatar-one-text-on-bg-fill":{value:ge[3]},"color-avatar-seven-bg-fill":{value:k[11]},"color-avatar-seven-text-on-bg-fill":{value:k[2]},"color-avatar-six-bg-fill":{value:se[9]},"color-avatar-six-text-on-bg-fill":{value:se[15]},"color-avatar-text-on-bg-fill":{value:e[1]},"color-avatar-three-bg-fill":{value:me[9]},"color-avatar-three-text-on-bg-fill":{value:me[15]},"color-avatar-two-bg-fill":{value:w[7]},"color-avatar-two-text-on-bg-fill":{value:w[14]},"color-backdrop-bg":{value:v[14]},"color-button-gradient-bg-fill":{value:"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)"},"color-checkbox-bg-surface-disabled":{value:v[7]},"color-checkbox-icon-disabled":{value:e[1]},"color-input-bg-surface":{value:e[2]},"color-input-bg-surface-hover":{value:e[3]},"color-input-bg-surface-active":{value:e[4]},"color-input-border":{value:e[12]},"color-input-border-hover":{value:e[13]},"color-input-border-active":{value:e[16]},"color-nav-bg":{value:e[7]},"color-nav-bg-surface":{value:v[3]},"color-nav-bg-surface-hover":{value:e[6]},"color-nav-bg-surface-active":{value:e[3]},"color-nav-bg-surface-selected":{value:e[3]},"color-radio-button-bg-surface-disabled":{value:v[7]},"color-radio-button-icon-disabled":{value:e[1]},"color-video-thumbnail-play-button-bg-fill-hover":{value:v[15]},"color-video-thumbnail-play-button-bg-fill":{value:v[14]},"color-video-thumbnail-play-button-text-on-bg-fill":{value:e[1]},"color-scrollbar-thumb-bg-hover":{value:e[12]}},tr={"font-family-sans":{value:"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif"},"font-family-mono":{value:"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace"},"font-size-275":{value:t[275]},"font-size-300":{value:t[300]},"font-size-325":{value:t[325]},"font-size-350":{value:t[350]},"font-size-400":{value:t[400]},"font-size-450":{value:t[450]},"font-size-500":{value:t[500]},"font-size-550":{value:t[550]},"font-size-600":{value:t[600]},"font-size-750":{value:t[750]},"font-size-800":{value:t[800]},"font-size-900":{value:t[900]},"font-size-1000":{value:t[1e3]},"font-weight-regular":{value:"450"},"font-weight-medium":{value:"550"},"font-weight-semibold":{value:"650"},"font-weight-bold":{value:"700"},"font-letter-spacing-densest":{value:"-0.54px"},"font-letter-spacing-denser":{value:"-0.3px"},"font-letter-spacing-dense":{value:"-0.2px"},"font-letter-spacing-normal":{value:"0px"},"font-line-height-300":{value:t[300]},"font-line-height-400":{value:t[400]},"font-line-height-500":{value:t[500]},"font-line-height-600":{value:t[600]},"font-line-height-700":{value:t[700]},"font-line-height-800":{value:t[800]},"font-line-height-1000":{value:t[1e3]},"font-line-height-1200":{value:t[1200]}},ar={"height-0":{value:t[0]},"height-025":{value:t["025"]},"height-050":{value:t["050"]},"height-100":{value:t[100]},"height-150":{value:t[150]},"height-200":{value:t[200]},"height-300":{value:t[300]},"height-400":{value:t[400]},"height-500":{value:t[500]},"height-600":{value:t[600]},"height-700":{value:t[700]},"height-800":{value:t[800]},"height-900":{value:t[900]},"height-1000":{value:t[1e3]},"height-1200":{value:t[1200]},"height-1600":{value:t[1600]},"height-2000":{value:t[2e3]},"height-2400":{value:t[2400]},"height-2800":{value:t[2800]},"height-3200":{value:t[3200]}},ir={"motion-duration-0":{value:"0ms"},"motion-duration-50":{value:"50ms"},"motion-duration-100":{value:"100ms"},"motion-duration-150":{value:"150ms"},"motion-duration-200":{value:"200ms"},"motion-duration-250":{value:"250ms"},"motion-duration-300":{value:"300ms"},"motion-duration-350":{value:"350ms"},"motion-duration-400":{value:"400ms"},"motion-duration-450":{value:"450ms"},"motion-duration-500":{value:"500ms"},"motion-duration-5000":{value:"5000ms"},"motion-ease":{value:"cubic-bezier(0.25, 0.1, 0.25, 1)",description:"Responds quickly and finishes with control. A great default for any user interaction."},"motion-ease-in":{value:"cubic-bezier(0.42, 0, 1, 1)",description:"Starts slowly and finishes at top speed. Use sparingly."},"motion-ease-out":{value:"cubic-bezier(0.19, 0.91, 0.38, 1)",description:"Starts at top speed and finishes slowly. Use sparingly."},"motion-ease-in-out":{value:"cubic-bezier(0.42, 0, 0.58, 1)",description:"Starts and finishes with equal speed. A good default for transitions triggered by the system."},"motion-linear":{value:"cubic-bezier(0, 0, 1, 1)",description:"Moves with constant speed. Use for continuous and mechanical animations, such as rotating spinners."},"motion-keyframes-bounce":{value:"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }"},"motion-keyframes-fade-in":{value:"{ to { opacity: 1 } }"},"motion-keyframes-pulse":{value:"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }"},"motion-keyframes-spin":{value:"{ to { transform: rotate(1turn) } }"},"motion-keyframes-appear-above":{value:"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }"},"motion-keyframes-appear-below":{value:"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"}},nr={"shadow-0":{value:"none"},"shadow-100":{value:"0px 1px 0px 0px rgba(26, 26, 26, 0.07)"},"shadow-200":{value:"0px 3px 1px -1px rgba(26, 26, 26, 0.07)"},"shadow-300":{value:"0px 4px 6px -2px rgba(26, 26, 26, 0.20)"},"shadow-400":{value:"0px 8px 16px -4px rgba(26, 26, 26, 0.22)"},"shadow-500":{value:"0px 12px 20px -8px rgba(26, 26, 26, 0.24)"},"shadow-600":{value:"0px 20px 20px -8px rgba(26, 26, 26, 0.28)"},"shadow-bevel-100":{value:"1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset"},"shadow-inset-100":{value:"0px 1px 2px 0px rgba(26, 26, 26, 0.15) inset, 0px 1px 1px 0px rgba(26, 26, 26, 0.15) inset"},"shadow-inset-200":{value:"0px 2px 1px 0px rgba(26, 26, 26, 0.20) inset, 1px 0px 1px 0px rgba(26, 26, 26, 0.12) inset, -1px 0px 1px 0px rgba(26, 26, 26, 0.12) inset"},"shadow-button":{value:"0px -1px 0px 0px #b5b5b5 inset, 0px 0px 0px 1px rgba(0, 0, 0, 0.1) inset, 0px 0.5px 0px 1.5px #FFF inset"},"shadow-button-hover":{value:"0px 1px 0px 0px #EBEBEB inset, -1px 0px 0px 0px #EBEBEB inset, 1px 0px 0px 0px #EBEBEB inset, 0px -1px 0px 0px #CCC inset"},"shadow-button-inset":{value:"-1px 0px 1px 0px rgba(26, 26, 26, 0.122) inset, 1px 0px 1px 0px rgba(26, 26, 26, 0.122) inset, 0px 2px 1px 0px rgba(26, 26, 26, 0.2) inset"},"shadow-button-primary":{value:"0px -1px 0px 1px rgba(0, 0, 0, 0.8) inset, 0px 0px 0px 1px rgba(48, 48, 48, 1) inset, 0px 0.5px 0px 1.5px rgba(255, 255, 255, 0.25) inset;"},"shadow-button-primary-hover":{value:"0px 1px 0px 0px rgba(255, 255, 255, 0.24) inset, 1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, -1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, 0px -1px 0px 0px #000 inset, 0px -1px 0px 1px #1A1A1A"},"shadow-button-primary-inset":{value:"0px 3px 0px 0px rgb(0, 0, 0) inset"},"shadow-button-primary-critical":{value:"0px -1px 0px 1px rgba(142, 31, 11, 0.8) inset, 0px 0px 0px 1px rgba(181, 38, 11, 0.8) inset, 0px 0.5px 0px 1.5px rgba(255, 255, 255, 0.349) inset"},"shadow-button-primary-critical-hover":{value:"0px 1px 0px 0px rgba(255, 255, 255, 0.48) inset, 1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, -1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, 0px -1.5px 0px 0px rgba(0, 0, 0, 0.25) inset"},"shadow-button-primary-critical-inset":{value:"-1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 0px 2px 0px 0px rgba(0, 0, 0, 0.6) inset"},"shadow-button-primary-success":{value:"0px -1px 0px 1px rgba(12, 81, 50, 0.8) inset, 0px 0px 0px 1px rgba(19, 111, 69, 0.8) inset, 0px 0.5px 0px 1.5px rgba(255, 255, 255, 0.251) inset"},"shadow-button-primary-success-hover":{value:"0px 1px 0px 0px rgba(255, 255, 255, 0.48) inset, 1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, -1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, 0px -1.5px 0px 0px rgba(0, 0, 0, 0.25) inset"},"shadow-button-primary-success-inset":{value:"-1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 0px 2px 0px 0px rgba(0, 0, 0, 0.6) inset"},"shadow-border-inset":{value:"0px 0px 0px 1px rgba(0, 0, 0, 0.08) inset"}},lr={"space-0":{value:t[0]},"space-025":{value:t["025"]},"space-050":{value:t["050"]},"space-100":{value:t[100]},"space-150":{value:t[150]},"space-200":{value:t[200]},"space-300":{value:t[300]},"space-400":{value:t[400]},"space-500":{value:t[500]},"space-600":{value:t[600]},"space-800":{value:t[800]},"space-1000":{value:t[1e3]},"space-1200":{value:t[1200]},"space-1600":{value:t[1600]},"space-2000":{value:t[2e3]},"space-2400":{value:t[2400]},"space-2800":{value:t[2800]},"space-3200":{value:t[3200]},"space-button-group-gap":{value:$("space-200")},"space-card-gap":{value:$("space-400")},"space-card-padding":{value:$("space-400")},"space-table-cell-padding":{value:$("space-150")}};function $(r){return"var("+ve(r)+")"}var cr={"text-heading-3xl-font-family":{value:i("font-family-sans")},"text-heading-3xl-font-size":{value:i("font-size-900")},"text-heading-3xl-font-weight":{value:i("font-weight-bold")},"text-heading-3xl-font-letter-spacing":{value:i("font-letter-spacing-densest")},"text-heading-3xl-font-line-height":{value:i("font-line-height-1200")},"text-heading-2xl-font-family":{value:i("font-family-sans")},"text-heading-2xl-font-size":{value:i("font-size-750")},"text-heading-2xl-font-weight":{value:i("font-weight-bold")},"text-heading-2xl-font-letter-spacing":{value:i("font-letter-spacing-denser")},"text-heading-2xl-font-line-height":{value:i("font-line-height-1000")},"text-heading-xl-font-family":{value:i("font-family-sans")},"text-heading-xl-font-size":{value:i("font-size-600")},"text-heading-xl-font-weight":{value:i("font-weight-bold")},"text-heading-xl-font-letter-spacing":{value:i("font-letter-spacing-dense")},"text-heading-xl-font-line-height":{value:i("font-line-height-800")},"text-heading-lg-font-family":{value:i("font-family-sans")},"text-heading-lg-font-size":{value:i("font-size-500")},"text-heading-lg-font-weight":{value:i("font-weight-semibold")},"text-heading-lg-font-letter-spacing":{value:i("font-letter-spacing-dense")},"text-heading-lg-font-line-height":{value:i("font-line-height-600")},"text-heading-md-font-family":{value:i("font-family-sans")},"text-heading-md-font-size":{value:i("font-size-350")},"text-heading-md-font-weight":{value:i("font-weight-semibold")},"text-heading-md-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-heading-md-font-line-height":{value:i("font-line-height-500")},"text-heading-sm-font-family":{value:i("font-family-sans")},"text-heading-sm-font-size":{value:i("font-size-325")},"text-heading-sm-font-weight":{value:i("font-weight-semibold")},"text-heading-sm-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-heading-sm-font-line-height":{value:i("font-line-height-500")},"text-heading-xs-font-family":{value:i("font-family-sans")},"text-heading-xs-font-size":{value:i("font-size-300")},"text-heading-xs-font-weight":{value:i("font-weight-semibold")},"text-heading-xs-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-heading-xs-font-line-height":{value:i("font-line-height-400")},"text-body-lg-font-family":{value:i("font-family-sans")},"text-body-lg-font-size":{value:i("font-size-350")},"text-body-lg-font-weight":{value:i("font-weight-regular")},"text-body-lg-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-body-lg-font-line-height":{value:i("font-line-height-500")},"text-body-md-font-family":{value:i("font-family-sans")},"text-body-md-font-size":{value:i("font-size-325")},"text-body-md-font-weight":{value:i("font-weight-regular")},"text-body-md-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-body-md-font-line-height":{value:i("font-line-height-500")},"text-body-sm-font-family":{value:i("font-family-sans")},"text-body-sm-font-size":{value:i("font-size-300")},"text-body-sm-font-weight":{value:i("font-weight-regular")},"text-body-sm-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-body-sm-font-line-height":{value:i("font-line-height-400")},"text-body-xs-font-family":{value:i("font-family-sans")},"text-body-xs-font-size":{value:i("font-size-275")},"text-body-xs-font-weight":{value:i("font-weight-regular")},"text-body-xs-font-letter-spacing":{value:i("font-letter-spacing-normal")},"text-body-xs-font-line-height":{value:i("font-line-height-300")}},sr={"width-0":{value:t[0]},"width-025":{value:t["025"]},"width-050":{value:t["050"]},"width-100":{value:t[100]},"width-150":{value:t[150]},"width-200":{value:t[200]},"width-300":{value:t[300]},"width-400":{value:t[400]},"width-500":{value:t[500]},"width-600":{value:t[600]},"width-700":{value:t[700]},"width-800":{value:t[800]},"width-900":{value:t[900]},"width-1000":{value:t[1e3]},"width-1200":{value:t[1200]},"width-1600":{value:t[1600]},"width-2000":{value:t[2e3]},"width-2400":{value:t[2400]},"width-2800":{value:t[2800]},"width-3200":{value:t[3200]}},gr={"z-index-0":{value:"auto"},"z-index-1":{value:"100"},"z-index-2":{value:"400"},"z-index-3":{value:"510"},"z-index-4":{value:"512"},"z-index-5":{value:"513"},"z-index-6":{value:"514"},"z-index-7":{value:"515"},"z-index-8":{value:"516"},"z-index-9":{value:"517"},"z-index-10":{value:"518"},"z-index-11":{value:"519"},"z-index-12":{value:"520"}},ye=Ke({border:rr,breakpoints:Le,color:or,font:tr,height:ar,motion:ir,shadow:nr,space:lr,text:cr,width:sr,zIndex:gr});function K(r){return Object.fromEntries(Object.entries(r).map(function(o){var a=X(o,2),n=a[0],l=a[1];return[n,l&&xe.includes(n)?pe(l):l]}))}function D(r){return er(ye,r)}function Ur(r){return"p-theme-"+r}function we(r){var o=new Set(Qe(r));return function(a){return o.has(a)}}we(ye);var ke="light",oe=ke,Fr=[ke,"light-mobile","light-high-contrast-experimental","dark-experimental"],ze=K({});D(ze);var Se=K({color:{"color-text":{value:e[16]},"color-text-secondary":{value:e[16]},"color-text-brand":{value:e[16]},"color-icon-secondary":{value:e[14]},"color-border":{value:e[12]},"color-input-border":{value:e[14]},"color-border-secondary":{value:e[12]},"color-bg-surface-secondary":{value:e[6]}},shadow:{"shadow-bevel-100":{value:"0px 1px 0px 0px rgba(26, 26, 26, 0.07), 0px 1px 0px 0px rgba(208, 208, 208, 0.40) inset, 1px 0px 0px 0px #CCC inset, -1px 0px 0px 0px #CCC inset, 0px -1px 0px 0px #999 inset"}}});D(Se);var J="0 0 0 "+i("border-width-025")+" "+i("color-border")+" inset",Ie=K({color:{"color-button-gradient-bg-fill":{value:"none"}},shadow:{"shadow-100":{value:"none"},"shadow-bevel-100":{value:"none"},"shadow-button":{value:J},"shadow-button-hover":{value:J},"shadow-button-inset":{value:J},"shadow-button-primary":{value:"none"},"shadow-button-primary-hover":{value:"none"},"shadow-button-primary-inset":{value:"none"},"shadow-button-primary-critical":{value:"none"},"shadow-button-primary-critical-hover":{value:"none"},"shadow-button-primary-critical-inset":{value:"none"},"shadow-button-primary-success":{value:"none"},"shadow-button-primary-success-hover":{value:"none"},"shadow-button-primary-success-inset":{value:"none"}},space:{"space-card-gap":{value:i("space-200")}},text:{"text-heading-2xl-font-size":{value:i("font-size-800")},"text-heading-xl-font-size":{value:i("font-size-550")},"text-heading-xl-font-line-height":{value:i("font-line-height-700")},"text-heading-lg-font-size":{value:i("font-size-450")},"text-heading-md-font-size":{value:i("font-size-400")},"text-heading-sm-font-size":{value:i("font-size-350")},"text-body-lg-font-size":{value:i("font-size-450")},"text-body-lg-font-line-height":{value:i("font-line-height-700")},"text-body-md-font-size":{value:i("font-size-400")},"text-body-md-font-line-height":{value:i("font-line-height-600")},"text-body-sm-font-size":{value:i("font-size-350")},"text-body-sm-font-line-height":{value:i("font-line-height-500")},"text-body-xs-font-size":{value:i("font-size-300")},"text-body-xs-font-line-height":{value:i("font-line-height-400")}}});D(Ie);var Te=K({color:{"color-scheme":{value:"dark"},"color-bg":{value:e[16]},"color-bg-surface":{value:e[15]},"color-bg-fill":{value:e[15]},"color-icon":{value:e[8]},"color-icon-secondary":{value:e[12]},"color-text":{value:e[8]},"color-text-secondary":{value:e[11]},"color-bg-surface-secondary-active":{value:e[13]},"color-bg-surface-secondary-hover":{value:e[14]},"color-bg-fill-transparent":{value:N[8]},"color-bg-fill-brand":{value:e[1]},"color-text-brand-on-bg-fill":{value:e[15]},"color-bg-surface-hover":{value:e[14]},"color-bg-fill-hover":{value:e[14]},"color-bg-fill-transparent-hover":{value:N[9]},"color-bg-fill-brand-hover":{value:e[5]},"color-bg-surface-selected":{value:e[13]},"color-bg-fill-selected":{value:e[13]},"color-bg-fill-transparent-selected":{value:N[11]},"color-bg-fill-brand-selected":{value:e[9]},"color-bg-surface-active":{value:e[13]},"color-bg-fill-active":{value:e[13]},"color-bg-fill-transparent-active":{value:N[10]},"color-bg-fill-brand-active":{value:e[4]},"color-bg-surface-brand-selected":{value:e[14]},"color-border-secondary":{value:e[13]},"color-tooltip-tail-down-border-experimental":{value:"rgba(60, 60, 60, 1)"},"color-tooltip-tail-up-border-experimental":{value:"rgba(71, 71, 71, 1)"},"color-border-gradient-experimental":{value:"linear-gradient(to bottom, "+N[9]+", "+N[4]+")"},"color-border-gradient-hover-experimental":{value:"linear-gradient(to bottom, "+N[9]+", "+N[4]+")"},"color-border-gradient-selected-experimental":{value:"linear-gradient(to bottom, "+v[10]+", "+N[10]+")"},"color-border-gradient-active-experimental":{value:"linear-gradient(to bottom, "+N[10]+", "+N[4]+")"}},shadow:{"shadow-bevel-100":{value:"1px 0px 0px 0px rgba(204, 204, 204, 0.08) inset, -1px 0px 0px 0px rgba(204, 204, 204, 0.08) inset, 0px -1px 0px 0px rgba(204, 204, 204, 0.08) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.16) inset"}}});D(Te);var br={light:ze,"light-mobile":Ie,"light-high-contrast-experimental":Se,"dark-experimental":Te},mr=br[oe];D(mr);var te={light:{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"light","color-bg":"rgba(241, 241, 241, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(255, 255, 255, 1)","color-bg-surface-hover":"rgba(247, 247, 247, 1)","color-bg-surface-active":"rgba(243, 243, 243, 1)","color-bg-surface-selected":"rgba(241, 241, 241, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(247, 247, 247, 1)","color-bg-surface-secondary-hover":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-active":"rgba(235, 235, 235, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(241, 241, 241, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(255, 255, 255, 1)","color-bg-fill-hover":"rgba(250, 250, 250, 1)","color-bg-fill-active":"rgba(247, 247, 247, 1)","color-bg-fill-selected":"rgba(204, 204, 204, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(48, 48, 48, 1)","color-bg-fill-brand-hover":"rgba(26, 26, 26, 1)","color-bg-fill-brand-active":"rgba(26, 26, 26, 1)","color-bg-fill-brand-selected":"rgba(48, 48, 48, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(0, 0, 0, 0.02)","color-bg-fill-transparent-hover":"rgba(0, 0, 0, 0.05)","color-bg-fill-transparent-active":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-selected":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(48, 48, 48, 1)","color-text-secondary":"rgba(97, 97, 97, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(74, 74, 74, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(255, 255, 255, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(227, 227, 227, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(235, 235, 235, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(212, 212, 212, 1)","color-tooltip-tail-up-border-experimental":"rgba(227, 227, 227, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-icon":"rgba(74, 74, 74, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(138, 138, 138, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(138, 138, 138, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07)","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0.09375rem #FFF inset","shadow-button-hover":"0rem 0.0625rem 0rem 0rem #EBEBEB inset, -0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0rem -0.0625rem 0rem 0rem #CCC inset","shadow-button-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset","shadow-button-primary":"0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;","shadow-button-primary-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.24) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.0625rem 0rem 0rem #000 inset, 0rem -0.0625rem 0rem 0.0625rem #1A1A1A","shadow-button-primary-inset":"0rem 0.1875rem 0rem 0rem rgb(0, 0, 0) inset","shadow-button-primary-critical":"0rem -0.0625rem 0rem 0.0625rem rgba(142, 31, 11, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(181, 38, 11, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.349) inset","shadow-button-primary-critical-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-critical-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-button-primary-success":"0rem -0.0625rem 0rem 0.0625rem rgba(12, 81, 50, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(19, 111, 69, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.251) inset","shadow-button-primary-success-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-success-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"1rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"1.875rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.5rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"2rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.25rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"0.875rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.8125rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"0.875rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.25rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"0.8125rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.25rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.75rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.6875rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"0.75rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}},"light-mobile":{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"light","color-bg":"rgba(241, 241, 241, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(255, 255, 255, 1)","color-bg-surface-hover":"rgba(247, 247, 247, 1)","color-bg-surface-active":"rgba(243, 243, 243, 1)","color-bg-surface-selected":"rgba(241, 241, 241, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(247, 247, 247, 1)","color-bg-surface-secondary-hover":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-active":"rgba(235, 235, 235, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(241, 241, 241, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(255, 255, 255, 1)","color-bg-fill-hover":"rgba(250, 250, 250, 1)","color-bg-fill-active":"rgba(247, 247, 247, 1)","color-bg-fill-selected":"rgba(204, 204, 204, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(48, 48, 48, 1)","color-bg-fill-brand-hover":"rgba(26, 26, 26, 1)","color-bg-fill-brand-active":"rgba(26, 26, 26, 1)","color-bg-fill-brand-selected":"rgba(48, 48, 48, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(0, 0, 0, 0.02)","color-bg-fill-transparent-hover":"rgba(0, 0, 0, 0.05)","color-bg-fill-transparent-active":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-selected":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(48, 48, 48, 1)","color-text-secondary":"rgba(97, 97, 97, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(74, 74, 74, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(255, 255, 255, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(227, 227, 227, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(235, 235, 235, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(212, 212, 212, 1)","color-tooltip-tail-up-border-experimental":"rgba(227, 227, 227, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-icon":"rgba(74, 74, 74, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(138, 138, 138, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"none","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(138, 138, 138, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"none","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"none","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0 0 0 var(--p-border-width-025) var(--p-color-border) inset","shadow-button-hover":"0 0 0 var(--p-border-width-025) var(--p-color-border) inset","shadow-button-inset":"0 0 0 var(--p-border-width-025) var(--p-color-border) inset","shadow-button-primary":"none","shadow-button-primary-hover":"none","shadow-button-primary-inset":"none","shadow-button-primary-critical":"none","shadow-button-primary-critical-hover":"none","shadow-button-primary-critical-inset":"none","shadow-button-primary-success":"none","shadow-button-primary-success-hover":"none","shadow-button-primary-success-inset":"none","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"0.5rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"2rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.375rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"1.75rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.125rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"1rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.875rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"1.125rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.75rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"1rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.5rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.875rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1.25rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.75rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"1rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}},"light-high-contrast-experimental":{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"light","color-bg":"rgba(241, 241, 241, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(255, 255, 255, 1)","color-bg-surface-hover":"rgba(247, 247, 247, 1)","color-bg-surface-active":"rgba(243, 243, 243, 1)","color-bg-surface-selected":"rgba(241, 241, 241, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-hover":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-active":"rgba(235, 235, 235, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(241, 241, 241, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(255, 255, 255, 1)","color-bg-fill-hover":"rgba(250, 250, 250, 1)","color-bg-fill-active":"rgba(247, 247, 247, 1)","color-bg-fill-selected":"rgba(204, 204, 204, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(48, 48, 48, 1)","color-bg-fill-brand-hover":"rgba(26, 26, 26, 1)","color-bg-fill-brand-active":"rgba(26, 26, 26, 1)","color-bg-fill-brand-selected":"rgba(48, 48, 48, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(0, 0, 0, 0.02)","color-bg-fill-transparent-hover":"rgba(0, 0, 0, 0.05)","color-bg-fill-transparent-active":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-selected":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(26, 26, 26, 1)","color-text-secondary":"rgba(26, 26, 26, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(26, 26, 26, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(255, 255, 255, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(138, 138, 138, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(138, 138, 138, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(212, 212, 212, 1)","color-tooltip-tail-up-border-experimental":"rgba(227, 227, 227, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-icon":"rgba(74, 74, 74, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(74, 74, 74, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(74, 74, 74, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07)","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07), 0rem 0.0625rem 0rem 0rem rgba(208, 208, 208, 0.40) inset, 0.0625rem 0rem 0rem 0rem #CCC inset, -0.0625rem 0rem 0rem 0rem #CCC inset, 0rem -0.0625rem 0rem 0rem #999 inset","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0.09375rem #FFF inset","shadow-button-hover":"0rem 0.0625rem 0rem 0rem #EBEBEB inset, -0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0rem -0.0625rem 0rem 0rem #CCC inset","shadow-button-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset","shadow-button-primary":"0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;","shadow-button-primary-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.24) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.0625rem 0rem 0rem #000 inset, 0rem -0.0625rem 0rem 0.0625rem #1A1A1A","shadow-button-primary-inset":"0rem 0.1875rem 0rem 0rem rgb(0, 0, 0) inset","shadow-button-primary-critical":"0rem -0.0625rem 0rem 0.0625rem rgba(142, 31, 11, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(181, 38, 11, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.349) inset","shadow-button-primary-critical-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-critical-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-button-primary-success":"0rem -0.0625rem 0rem 0.0625rem rgba(12, 81, 50, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(19, 111, 69, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.251) inset","shadow-button-primary-success-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-success-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"1rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"1.875rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.5rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"2rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.25rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"0.875rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.8125rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"0.875rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.25rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"0.8125rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.25rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.75rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.6875rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"0.75rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}},"dark-experimental":{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"dark","color-bg":"rgba(26, 26, 26, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(48, 48, 48, 1)","color-bg-surface-hover":"rgba(74, 74, 74, 1)","color-bg-surface-active":"rgba(97, 97, 97, 1)","color-bg-surface-selected":"rgba(97, 97, 97, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(247, 247, 247, 1)","color-bg-surface-secondary-hover":"rgba(74, 74, 74, 1)","color-bg-surface-secondary-active":"rgba(97, 97, 97, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(74, 74, 74, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(48, 48, 48, 1)","color-bg-fill-hover":"rgba(74, 74, 74, 1)","color-bg-fill-active":"rgba(97, 97, 97, 1)","color-bg-fill-selected":"rgba(97, 97, 97, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(255, 255, 255, 1)","color-bg-fill-brand-hover":"rgba(243, 243, 243, 1)","color-bg-fill-brand-active":"rgba(247, 247, 247, 1)","color-bg-fill-brand-selected":"rgba(212, 212, 212, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(255, 255, 255, 0.11)","color-bg-fill-transparent-hover":"rgba(255, 255, 255, 0.17)","color-bg-fill-transparent-active":"rgba(255, 255, 255, 0.20)","color-bg-fill-transparent-selected":"rgba(255, 255, 255, 0.28)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(227, 227, 227, 1)","color-text-secondary":"rgba(181, 181, 181, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(74, 74, 74, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(227, 227, 227, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(97, 97, 97, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(60, 60, 60, 1)","color-tooltip-tail-up-border-experimental":"rgba(71, 71, 71, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(255, 255, 255, 0.17), rgba(255, 255, 255, 0.03))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(255, 255, 255, 0.17), rgba(255, 255, 255, 0.03))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(0, 0, 0, 0.20), rgba(255, 255, 255, 0.20))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(255, 255, 255, 0.20), rgba(255, 255, 255, 0.03))","color-icon":"rgba(227, 227, 227, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(138, 138, 138, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(138, 138, 138, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07)","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"0.0625rem 0rem 0rem 0rem rgba(204, 204, 204, 0.08) inset, -0.0625rem 0rem 0rem 0rem rgba(204, 204, 204, 0.08) inset, 0rem -0.0625rem 0rem 0rem rgba(204, 204, 204, 0.08) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.16) inset","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0.09375rem #FFF inset","shadow-button-hover":"0rem 0.0625rem 0rem 0rem #EBEBEB inset, -0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0rem -0.0625rem 0rem 0rem #CCC inset","shadow-button-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset","shadow-button-primary":"0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;","shadow-button-primary-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.24) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.0625rem 0rem 0rem #000 inset, 0rem -0.0625rem 0rem 0.0625rem #1A1A1A","shadow-button-primary-inset":"0rem 0.1875rem 0rem 0rem rgb(0, 0, 0) inset","shadow-button-primary-critical":"0rem -0.0625rem 0rem 0.0625rem rgba(142, 31, 11, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(181, 38, 11, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.349) inset","shadow-button-primary-critical-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-critical-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-button-primary-success":"0rem -0.0625rem 0rem 0.0625rem rgba(12, 81, 50, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(19, 111, 69, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.251) inset","shadow-button-primary-success-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-success-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"1rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"1.875rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.5rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"2rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.25rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"0.875rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.8125rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"0.875rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.25rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"0.8125rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.25rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.75rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.6875rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"0.75rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}}},Ue=te[oe];we(te[oe]);const dr=S.createContext(null),fr=S.createContext(null);function Mr(r){return te[r]}function Br(){const r=S.useContext(dr);if(!r)throw new Error("No theme was provided. Your application must be wrapped in an <AppProvider> or <ThemeProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return r}function Er(){const r=S.useContext(fr);if(!r)throw new Error("No themeName was provided. Your application must be wrapped in an <AppProvider> or <ThemeProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return r}const Fe=typeof window>"u"||typeof document>"u",ur=Fe?S.useEffect:S.useLayoutEffect,Me={navigationBarCollapsed:"767.95px",stackedContent:"1039.95px"},Be={media:"",addListener:_,removeListener:_,matches:!1,onchange:_,addEventListener:_,removeEventListener:_,dispatchEvent:r=>!0};function _(){}function Nr(){return typeof window>"u"?Be:window.matchMedia(`(max-width: ${Me.navigationBarCollapsed})`)}function hr(){return typeof window>"u"?Be:window.matchMedia(`(max-width: ${Me.stackedContent})`)}const ee=pr(Ue.breakpoints);function de(r,o){return Object.fromEntries(!Fe&&!o?ee.map(([a,n])=>[a,window.matchMedia(n).matches]):ee.map(([a])=>[a,!1]))}function Rr(r){const[o,a]=S.useState(de(r==null?void 0:r.defaults,!0));return ur(()=>{const n=ee.map(([g,d])=>window.matchMedia(d)),l=()=>a(de());return n.forEach(g=>{g.addListener?g.addListener(l):g.addEventListener("change",l)}),l(),()=>{n.forEach(g=>{g.removeListener?g.removeListener(l):g.removeEventListener("change",l)})}},[]),o}function pr(r){return Object.entries(Ve(r)).map(([a,n])=>Object.entries(n).map(([l,g])=>[`${a.split("-")[1]}${vr(l)}`,g])).flat()}function vr(r){return r.charAt(0).toUpperCase()+r.slice(1)}function fe(r,o,a){let n,l,g,d,b,p,x=0,B=!1,M=!1,A=!0;const O=!o&&o!==0;if(typeof r!="function")throw new TypeError("Expected a function");const R=o||0;typeof a=="object"&&(B=!!a.leading,M="maxWait"in a,g=M?Math.max(Number(a.maxWait)||0,R):void 0,A="trailing"in a?!!a.trailing:A);function I(f){const E=n,H=l;return n=void 0,l=void 0,x=f,d=r.apply(H,E),d}function C(f,E){return O?(cancelAnimationFrame(b),requestAnimationFrame(f)):setTimeout(f,E)}function c(f){if(O)return cancelAnimationFrame(f);clearTimeout(f)}function s(f){return x=f,b=C(h,R),B?I(f):d}function m(f){const E=f-p,H=f-x,ae=R-E;return M&&g?Math.min(ae,g-H):ae}function T(f){const E=f-p,H=f-x;return p===void 0||E>=R||E<0||M&&g&&H>=g}function h(){const f=Date.now();if(T(f))return Y(f);b=C(h,m(f))}function Y(f){return b=void 0,A&&n?I(f):(n=l=void 0,d)}function Ne(){b!==void 0&&c(b),x=0,n=p=l=b=void 0}function Re(){return b===void 0?d:Y(Date.now())}function Ce(){return b!==void 0}function W(...f){const E=Date.now(),H=T(E);if(n=f,l=this,p=E,H){if(b===void 0)return s(p);if(M)return b=C(h,R),I(p)}return b===void 0&&(b=C(h,R)),d}return W.cancel=Ne,W.flush=Re,W.pending=Ce,W}class V{static get zero(){return new V}constructor({top:o=0,left:a=0,width:n=0,height:l=0}={}){this.top=o,this.left=a,this.width=n,this.height=l}get center(){return{x:this.left+this.width/2,y:this.top+this.height/2}}}function G(r){if(!(r instanceof Element))return new V({width:window.innerWidth,height:window.innerHeight});const o=r.getBoundingClientRect();return new V({top:o.top,left:o.left,width:o.width,height:o.height})}const q=1e3/60;class Cr{constructor(o){this.stickyItems=[],this.stuckItems=[],this.container=null,this.topBarOffset=0,this.handleResize=fe(()=>{this.manageStickyItems()},q,{leading:!0,trailing:!0,maxWait:q}),this.handleScroll=fe(()=>{this.manageStickyItems()},q,{leading:!0,trailing:!0,maxWait:q}),o&&this.setContainer(o)}registerStickyItem(o){this.stickyItems.push(o)}unregisterStickyItem(o){const a=this.stickyItems.findIndex(({stickyNode:n})=>o===n);this.stickyItems.splice(a,1)}setContainer(o){this.container=o,Ee(o)&&this.setTopBarOffset(o),this.container.addEventListener("scroll",this.handleScroll),window.addEventListener("resize",this.handleResize),this.manageStickyItems()}removeScrollListener(){this.container&&(this.container.removeEventListener("scroll",this.handleScroll),window.removeEventListener("resize",this.handleResize))}manageStickyItems(){if(this.stickyItems.length<=0)return;const o=this.container?xr(this.container):0,a=G(this.container).top+this.topBarOffset;this.stickyItems.forEach(n=>{const{handlePositioning:l}=n,{sticky:g,top:d,left:b,width:p}=this.evaluateStickyItem(n,o,a);this.updateStuckItems(n,g),l(g,d,b,p)})}evaluateStickyItem(o,a,n){var C;const{stickyNode:l,placeHolderNode:g,boundingElement:d,offset:b,disableWhenStacked:p}=o;if(p&&hr().matches)return{sticky:!1,top:0,left:0,width:"auto"};const x=b?this.getOffset(l)+parseInt(Ue.space["space-500"],10):this.getOffset(l),B=a+x,M=g.getBoundingClientRect().top-n+a,A=n+x,O=g.getBoundingClientRect().width,R=g.getBoundingClientRect().left;let I;if(d==null)I=B>=M;else{const c=l.getBoundingClientRect().height||((C=l.firstElementChild)==null?void 0:C.getBoundingClientRect().height)||0,s=d.getBoundingClientRect().bottom-c+a-n;I=B>=M&&B<s}return{sticky:I,top:A,left:R,width:O}}updateStuckItems(o,a){const{stickyNode:n}=o;a&&!this.isNodeStuck(n)?this.addStuckItem(o):!a&&this.isNodeStuck(n)&&this.removeStuckItem(o)}addStuckItem(o){this.stuckItems.push(o)}removeStuckItem(o){const{stickyNode:a}=o,n=this.stuckItems.findIndex(({stickyNode:l})=>a===l);this.stuckItems.splice(n,1)}getOffset(o){if(this.stuckItems.length===0)return 0;let a=0,n=0;const l=this.stuckItems.length,g=G(o);for(;n<l;){const d=this.stuckItems[n].stickyNode;if(d!==o){const b=G(d);yr(g,b)||(a+=G(d).height)}else break;n++}return a}isNodeStuck(o){return this.stuckItems.findIndex(({stickyNode:n})=>o===n)>=0}setTopBarOffset(o){const a=o.querySelector(`:not(${He.selector}) ${Oe.selector}`);this.topBarOffset=a?a.clientHeight:0}}function Ee(r){return r===document}function xr(r){return Ee(r)?document.body.scrollTop||document.documentElement.scrollTop:r.scrollTop}function yr(r,o){const a=r.left,n=r.left+r.width,l=o.left;return o.left+o.width<a||n<l}const Ar=S.createContext(void 0),Hr=S.createContext(void 0),Or=S.createContext(void 0),Lr=S.createContext(void 0);class jr extends S.PureComponent{componentDidMount(){this.attachListener()}componentDidUpdate({passive:o,...a}){this.detachListener(a),this.attachListener()}componentWillUnmount(){this.detachListener()}render(){return null}attachListener(){const{event:o,handler:a,capture:n,passive:l}=this.props;window.addEventListener(o,a,{capture:n,passive:l})}detachListener(o){const{event:a,handler:n,capture:l}=o||this.props;window.removeEventListener(a,n,l)}}function Pr(){const[r,o]=S.useState(!1);return S.useEffect(()=>{o(!0)},[]),r}const _r=S.createContext(void 0),Dr=S.createContext(void 0);export{jr as E,Ar as I,Or as L,Lr as M,_r as P,V as R,Cr as S,fr as T,Pr as a,Dr as b,Ur as c,fe as d,oe as e,dr as f,Mr as g,Hr as h,Fe as i,Tr as j,ur as k,Sr as l,Er as m,Nr as n,G as o,Oe as p,zr as q,Br as r,He as s,Fr as t,Rr as u,kr as v,Ue as w,Ir as x};
