# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "3705ec0d683e06826b5b05053d2fb0a0"
name = "chatBot"
handle = "chatbot-app"
application_url = "https://racing-dreams-george-largely.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products,write_script_tags,read_script_tags"

[auth]
redirect_urls = ["https://racing-dreams-george-largely.trycloudflare.com/auth/callback", "https://racing-dreams-george-largely.trycloudflare.com/auth/shopify/callback", "https://racing-dreams-george-largely.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
