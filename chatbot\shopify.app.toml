# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "fc5ccb3037e1322db6915a4de00972c1"
name = "chatBot"
handle = "chatbot-252"
application_url = "https://distributions-italian-directors-then.trycloudflare.com"
embedded = true

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_script_tags,read_script_tags"

[auth]
redirect_urls = ["https://distributions-italian-directors-then.trycloudflare.com/auth/callback", "https://distributions-italian-directors-then.trycloudflare.com/auth/shopify/callback", "https://distributions-italian-directors-then.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false
