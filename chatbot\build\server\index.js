var _a;
import { jsx, jsxs } from "react/jsx-runtime";
import { PassThrough } from "node:stream";
import { createReadableStreamFromReadable, redirect } from "@remix-run/node";
import { RemixServer, Meta, Links, Outlet, ScrollRestoration, Scripts, useLoaderData, useActionData, Form, Link, useRouteError, useFetcher } from "@remix-run/react";
import { isbot } from "isbot";
import { renderToPipeableStream } from "react-dom/server";
import "@shopify/shopify-app-remix/adapters/node";
import { shopifyApp, AppDistribution, ApiVersion, LoginErrorType, boundary } from "@shopify/shopify-app-remix/server";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { PrismaClient } from "@prisma/client";
import { useState, useEffect } from "react";
import { A<PERSON><PERSON><PERSON><PERSON>, <PERSON>, Card, FormLayout, Text, TextField, Button, Layout, BlockStack, Link as Link$1, List, Box, Banner, InlineStack, Badge, Spinner } from "@shopify/polaris";
import { AppProvider as AppProvider$1 } from "@shopify/shopify-app-remix/react";
import { NavMenu, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
const ABORT_DELAY = 5e3;
function handleRequest(request, responseStatusCode, responseHeaders, remixContext, loadContext) {
  return isbot(request.headers.get("user-agent") || "") ? handleBotRequest(
    request,
    responseStatusCode,
    responseHeaders,
    remixContext
  ) : handleBrowserRequest(
    request,
    responseStatusCode,
    responseHeaders,
    remixContext
  );
}
function handleBotRequest(request, responseStatusCode, responseHeaders, remixContext) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsx(
        RemixServer,
        {
          context: remixContext,
          url: request.url,
          abortDelay: ABORT_DELAY
        }
      ),
      {
        onAllReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");
          responseHeaders.delete("X-Frame-Options");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          if (shellRendered) {
            console.error(error);
          }
        }
      }
    );
    setTimeout(abort, ABORT_DELAY);
  });
}
function handleBrowserRequest(request, responseStatusCode, responseHeaders, remixContext) {
  return new Promise((resolve, reject) => {
    let shellRendered = false;
    const { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsx(
        RemixServer,
        {
          context: remixContext,
          url: request.url,
          abortDelay: ABORT_DELAY
        }
      ),
      {
        onShellReady() {
          shellRendered = true;
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");
          responseHeaders.delete("X-Frame-Options");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          if (shellRendered) {
            console.error(error);
          }
        }
      }
    );
    setTimeout(abort, ABORT_DELAY);
  });
}
const entryServer = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: handleRequest
}, Symbol.toStringTag, { value: "Module" }));
function App$2() {
  return /* @__PURE__ */ jsxs("html", { children: [
    /* @__PURE__ */ jsxs("head", { children: [
      /* @__PURE__ */ jsx("meta", { charSet: "utf-8" }),
      /* @__PURE__ */ jsx("meta", { name: "viewport", content: "width=device-width,initial-scale=1" }),
      /* @__PURE__ */ jsx("link", { rel: "preconnect", href: "https://cdn.shopify.com/" }),
      /* @__PURE__ */ jsx(
        "link",
        {
          rel: "stylesheet",
          href: "https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        }
      ),
      /* @__PURE__ */ jsx(Meta, {}),
      /* @__PURE__ */ jsx(Links, {})
    ] }),
    /* @__PURE__ */ jsxs("body", { children: [
      /* @__PURE__ */ jsx(Outlet, {}),
      /* @__PURE__ */ jsx(ScrollRestoration, {}),
      /* @__PURE__ */ jsx(Scripts, {})
    ] })
  ] });
}
const route0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: App$2
}, Symbol.toStringTag, { value: "Module" }));
if (process.env.NODE_ENV !== "production") {
  if (!global.prismaGlobal) {
    global.prismaGlobal = new PrismaClient();
  }
}
const prisma = global.prismaGlobal ?? new PrismaClient();
const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: (_a = process.env.SCOPES) == null ? void 0 : _a.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new PrismaSessionStorage(prisma),
  distribution: AppDistribution.AppStore,
  future: {
    unstable_newEmbeddedAuthStrategy: true,
    removeRest: true
  },
  ...process.env.SHOP_CUSTOM_DOMAIN ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] } : {}
});
ApiVersion.January25;
shopify.addDocumentResponseHeaders;
const authenticate = shopify.authenticate;
shopify.unauthenticated;
const login = shopify.login;
shopify.registerWebhooks;
shopify.sessionStorage;
const action$8 = async ({ request }) => {
  const { payload, session, topic, shop } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);
  const current = payload.current;
  if (session) {
    await prisma.session.update({
      where: {
        id: session.id
      },
      data: {
        scope: current.toString()
      }
    });
  }
  return new Response();
};
const route1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$8
}, Symbol.toStringTag, { value: "Module" }));
const action$7 = async ({ request }) => {
  try {
    const { shop, session, topic, payload } = await authenticate.webhook(request);
    console.log(`Received ${topic} webhook for ${shop}`);
    if (!payload) {
      console.error("No payload received for customer create webhook");
      return new Response("No payload", { status: 400 });
    }
    const customer = payload;
    try {
      await prisma.customer.upsert({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customer.id.toString(),
            shop
          }
        },
        update: {
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          accepts_marketing: customer.accepts_marketing,
          created_at: new Date(customer.created_at),
          updated_at: new Date(customer.updated_at),
          last_seen: /* @__PURE__ */ new Date()
        },
        create: {
          shopify_customer_id: customer.id.toString(),
          shop,
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          accepts_marketing: customer.accepts_marketing,
          created_at: new Date(customer.created_at),
          updated_at: new Date(customer.updated_at),
          last_seen: /* @__PURE__ */ new Date()
        }
      });
      console.log(`Customer ${customer.id} data stored/updated for shop ${shop}`);
    } catch (dbError) {
      console.error(`Failed to store customer data for ${shop}:`, dbError);
    }
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("Error processing customer create webhook:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};
const route2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$7
}, Symbol.toStringTag, { value: "Module" }));
const action$6 = async ({ request }) => {
  try {
    const { shop, session, topic, payload } = await authenticate.webhook(request);
    console.log(`Received ${topic} webhook for ${shop}`);
    if (!payload) {
      console.error("No payload received for customer update webhook");
      return new Response("No payload", { status: 400 });
    }
    const customer = payload;
    try {
      await prisma.customer.upsert({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customer.id.toString(),
            shop
          }
        },
        update: {
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          accepts_marketing: customer.accepts_marketing,
          created_at: new Date(customer.created_at),
          updated_at: new Date(customer.updated_at),
          last_seen: /* @__PURE__ */ new Date()
        },
        create: {
          shopify_customer_id: customer.id.toString(),
          shop,
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          accepts_marketing: customer.accepts_marketing,
          created_at: new Date(customer.created_at),
          updated_at: new Date(customer.updated_at),
          last_seen: /* @__PURE__ */ new Date()
        }
      });
      console.log(`Customer ${customer.id} data updated for shop ${shop}`);
    } catch (dbError) {
      console.error(`Failed to update customer data for ${shop}:`, dbError);
    }
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("Error processing customer update webhook:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};
const route3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$6
}, Symbol.toStringTag, { value: "Module" }));
const loader$9 = async ({ request, params }) => {
  try {
    const { customerId } = params;
    const url = new URL(request.url);
    const shop = url.searchParams.get("shop");
    if (!customerId || !shop) {
      return Response.json({ error: "Customer ID and shop are required" }, { status: 400 });
    }
    try {
      const customer = await prisma.customer.findUnique({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customerId,
            shop
          }
        }
      });
      if (!customer) {
        return Response.json({ error: "Customer not found" }, { status: 404 });
      }
      await prisma.customer.update({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customerId,
            shop
          }
        },
        data: {
          last_seen: /* @__PURE__ */ new Date()
        }
      });
      return Response.json({
        id: customer.shopify_customer_id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        phone: customer.phone,
        accepts_marketing: customer.accepts_marketing,
        created_at: customer.created_at,
        last_seen: customer.last_seen
      });
    } catch (dbError) {
      console.error(`Failed to get customer data:`, dbError);
      return Response.json({ error: "Database error" }, { status: 500 });
    }
  } catch (error) {
    console.error("Error in customer API:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};
const action$5 = async ({ request, params }) => {
  try {
    const { customerId } = params;
    const url = new URL(request.url);
    const shop = url.searchParams.get("shop");
    const formData = await request.formData();
    const action2 = formData.get("action");
    if (!customerId || !shop) {
      return Response.json({ error: "Customer ID and shop are required" }, { status: 400 });
    }
    switch (action2) {
      case "update_last_seen":
        try {
          await prisma.customer.update({
            where: {
              shopify_customer_id_shop: {
                shopify_customer_id: customerId,
                shop
              }
            },
            data: {
              last_seen: /* @__PURE__ */ new Date()
            }
          });
          return Response.json({ success: true, message: "Last seen updated" });
        } catch (dbError) {
          console.error(`Failed to update last seen:`, dbError);
          return Response.json({ error: "Database error" }, { status: 500 });
        }
      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in customer API action:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};
const route4 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$5,
  loader: loader$9
}, Symbol.toStringTag, { value: "Module" }));
const action$4 = async ({ request }) => {
  const { shop, session, topic } = await authenticate.webhook(request);
  console.log(`Received ${topic} webhook for ${shop}`);
  if (session) {
    await prisma.session.deleteMany({ where: { shop } });
  }
  return new Response();
};
const route5 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$4
}, Symbol.toStringTag, { value: "Module" }));
const action$3 = async ({ request }) => {
  try {
    const formData = await request.formData();
    const customerId = formData.get("customerId");
    const shop = formData.get("shop");
    const email = formData.get("email");
    const firstName = formData.get("firstName");
    const lastName = formData.get("lastName");
    const action2 = formData.get("action");
    if (!customerId || !shop) {
      return Response.json({ error: "Customer ID and shop are required" }, { status: 400 });
    }
    try {
      switch (action2) {
        case "track_customer":
          await prisma.customer.upsert({
            where: {
              shopify_customer_id_shop: {
                shopify_customer_id: customerId,
                shop
              }
            },
            update: {
              email: email || void 0,
              first_name: firstName || void 0,
              last_name: lastName || void 0,
              last_seen: /* @__PURE__ */ new Date()
            },
            create: {
              shopify_customer_id: customerId,
              shop,
              email: email || null,
              first_name: firstName || null,
              last_name: lastName || null,
              phone: null,
              accepts_marketing: false,
              created_at: /* @__PURE__ */ new Date(),
              updated_at: /* @__PURE__ */ new Date(),
              last_seen: /* @__PURE__ */ new Date()
            }
          });
          console.log(`Customer ${customerId} tracked for shop ${shop}`);
          return Response.json({ success: true, message: "Customer tracked successfully" });
        case "update_last_seen":
          await prisma.customer.update({
            where: {
              shopify_customer_id_shop: {
                shopify_customer_id: customerId,
                shop
              }
            },
            data: {
              last_seen: /* @__PURE__ */ new Date()
            }
          });
          return Response.json({ success: true, message: "Last seen updated" });
        default:
          return Response.json({ error: "Invalid action" }, { status: 400 });
      }
    } catch (dbError) {
      console.error(`Failed to track customer data:`, dbError);
      return Response.json({ error: "Database error" }, { status: 500 });
    }
  } catch (error) {
    console.error("Error in customer tracking API:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};
const route6 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$3
}, Symbol.toStringTag, { value: "Module" }));
async function loader$8({ request }) {
  const url = new URL(request.url);
  const appUrl = process.env.SHOPIFY_APP_URL || url.origin;
  const script = `
(function () {
  // Prevent multiple initializations
  if (window.ChatbotLoaderInitialized) {
    return;
  }
  window.ChatbotLoaderInitialized = true;

  // Configuration
  const config = {
    // Replace with your actual chatbot embed script URL
    embedScriptUrl: "https://ohio-instrumental-spoken-towers.trycloudflare.com/chatbot-embed.js",
    // App URL for API calls if needed
    appUrl: "${appUrl}",
    // Timeout for script loading (in milliseconds)
    loadTimeout: 10000,
    // Retry attempts if script fails to load
    maxRetries: 3
  };

  let retryCount = 0;

  function loadChatbotScript() {
    const script = document.createElement("script");
    script.src = config.embedScriptUrl;
    script.async = true;
    script.defer = true;

    // Set up timeout
    const timeoutId = setTimeout(() => {
      console.warn("Chatbot script loading timed out");
      handleScriptError();
    }, config.loadTimeout);

    script.onload = function () {
      clearTimeout(timeoutId);
      initializeChatbot();
    };

    script.onerror = function () {
      clearTimeout(timeoutId);
      console.error("Failed to load chatbot script");
      handleScriptError();
    };

    document.head.appendChild(script);
  }

  function handleScriptError() {
    retryCount++;
    if (retryCount < config.maxRetries) {
      console.log(\`Retrying chatbot script load (attempt \${retryCount + 1}/\${config.maxRetries})\`);
      setTimeout(loadChatbotScript, 2000 * retryCount); // Exponential backoff
    } else {
      console.error("Failed to load chatbot script after maximum retries");
    }
  }

  function initializeChatbot() {
    try {
      // Get customer information from multiple Shopify sources
      let customerId = null;
      let customerEmail = null;
      let customerFirstName = null;
      let customerLastName = null;

      // Method 1: Check Shopify.customer (most common)
      if (window.Shopify?.customer?.id) {
        customerId = window.Shopify.customer.id;
        customerEmail = window.Shopify.customer.email;
        customerFirstName = window.Shopify.customer.first_name;
        customerLastName = window.Shopify.customer.last_name;
      }
      // Method 2: Check for customer data in meta tags
      else {
        const customerIdMeta = document.querySelector('meta[name="shopify-customer-id"]');
        const customerEmailMeta = document.querySelector('meta[name="shopify-customer-email"]');
        const customerFirstNameMeta = document.querySelector('meta[name="shopify-customer-first-name"]');
        const customerLastNameMeta = document.querySelector('meta[name="shopify-customer-last-name"]');

        if (customerIdMeta?.content) {
          customerId = customerIdMeta.content;
          customerEmail = customerEmailMeta?.content || null;
          customerFirstName = customerFirstNameMeta?.content || null;
          customerLastName = customerLastNameMeta?.content || null;
        }
      }

      // Method 3: Check for customer data in data attributes on body or html
      if (!customerId) {
        const bodyCustomerId = document.body?.dataset?.customerId || document.documentElement?.dataset?.customerId;
        if (bodyCustomerId) {
          customerId = bodyCustomerId;
          customerEmail = document.body?.dataset?.customerEmail || document.documentElement?.dataset?.customerEmail || null;
          customerFirstName = document.body?.dataset?.customerFirstName || document.documentElement?.dataset?.customerFirstName || null;
          customerLastName = document.body?.dataset?.customerLastName || document.documentElement?.dataset?.customerLastName || null;
        }
      }

      // Get shop information
      const shopDomain = window.Shopify?.shop || window.location.hostname;

      // Prepare chatbot configuration
      const chatbotConfig = {
        customerId: customerId,
        customer: {
          id: customerId,
          email: customerEmail,
          firstName: customerFirstName,
          lastName: customerLastName,
          isLoggedIn: !!customerId
        },
        shop: {
          domain: shopDomain
        },
        // Add current page context
        page: {
          url: window.location.href,
          path: window.location.pathname,
          title: document.title
        },
        // App configuration
        app: {
          url: config.appUrl
        }
      };

      // Initialize chatbot if available
      if (window.Chatbot && typeof window.Chatbot.init === "function") {
        window.Chatbot.init(chatbotConfig);
        console.log("Chatbot initialized successfully", chatbotConfig);
      } else {
        console.warn("Chatbot object not found or init method not available");
      }
    } catch (error) {
      console.error("Error initializing chatbot:", error);
    }
  }

  // Start loading the chatbot script
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadChatbotScript);
  } else {
    loadChatbotScript();
  }
})();
  `.trim();
  return new Response(script, {
    headers: {
      "Content-Type": "application/javascript",
      "Cache-Control": "public, max-age=3600"
      // Cache for 1 hour
    }
  });
}
const route7 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$8
}, Symbol.toStringTag, { value: "Module" }));
async function loader$7({ request }) {
  const script = `
// Chatbot Embed Script
(function() {
  'use strict';
  
  // Create chatbot namespace
  window.Chatbot = window.Chatbot || {};
  
  // Chatbot configuration and initialization
  window.Chatbot.init = function(config) {
    console.log('🤖 Chatbot initializing with config:', config);

    // Store configuration
    window.Chatbot.config = config;

    // If customer is logged in, track and fetch customer data
    if (config.customerId && config.shop) {
      // First track the customer interaction
      trackCustomer(config.customerId, config.shop.domain, config.customer)
        .then(() => {
          // Then fetch any existing customer data
          return fetchCustomerData(config.customerId, config.shop.domain);
        })
        .then(customerData => {
          config.customerData = customerData;
          console.log('📊 Customer data loaded:', customerData);
          createChatbotUI(config);
        })
        .catch(error => {
          console.warn('⚠️ Failed to load customer data:', error);
          createChatbotUI(config);
        });
    } else {
      createChatbotUI(config);
    }
  };

  async function trackCustomer(customerId, shop, customer) {
    try {
      const formData = new FormData();
      formData.append('action', 'track_customer');
      formData.append('customerId', customerId);
      formData.append('shop', shop);
      if (customer.email) formData.append('email', customer.email);
      if (customer.firstName) formData.append('firstName', customer.firstName);
      if (customer.lastName) formData.append('lastName', customer.lastName);

      await fetch(\`\${config.app.url}/api/customer/track\`, {
        method: 'POST',
        body: formData
      });
    } catch (error) {
      console.error('Error tracking customer:', error);
    }
  }

  async function fetchCustomerData(customerId, shop) {
    try {
      const response = await fetch(\`\${config.app.url}/api/customer/\${customerId}?shop=\${shop}\`);
      if (response.ok) {
        return await response.json();
      }
      throw new Error('Failed to fetch customer data');
    } catch (error) {
      console.error('Error fetching customer data:', error);
      throw error;
    }
  }

  async function updateLastSeen(customerId, shop) {
    try {
      const formData = new FormData();
      formData.append('action', 'update_last_seen');
      formData.append('customerId', customerId);
      formData.append('shop', shop);

      await fetch(\`\${config.app.url}/api/customer/track\`, {
        method: 'POST',
        body: formData
      });
    } catch (error) {
      console.error('Error updating last seen:', error);
    }
  }
  
  function createChatbotUI(config) {
    // Remove existing chatbot if present
    const existingChatbot = document.getElementById('shopify-chatbot');
    if (existingChatbot) {
      existingChatbot.remove();
    }
    
    // Create chatbot container
    const chatbotContainer = document.createElement('div');
    chatbotContainer.id = 'shopify-chatbot';
    chatbotContainer.innerHTML = \`
      <div id="chatbot-widget" style="
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: all 0.3s ease;
      ">
        <svg width="24" height="24" fill="white" viewBox="0 0 24 24">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
        </svg>
      </div>
      
      <div id="chatbot-panel" style="
        position: fixed;
        bottom: 90px;
        right: 20px;
        width: 350px;
        height: 500px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        display: none;
        flex-direction: column;
        z-index: 9998;
        overflow: hidden;
      ">
        <div style="
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 16px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <h3 style="margin: 0; font-size: 16px;">Chat Support</h3>
          <p style="margin: 4px 0 0 0; font-size: 12px; opacity: 0.9;">
            \${config.customer.isLoggedIn ?
              \`Hi \${config.customerData?.first_name || config.customer.firstName || 'there'}!\` :
              'How can we help you?'}
          </p>
        </div>
        
        <div id="chat-messages" style="
          flex: 1;
          padding: 16px;
          overflow-y: auto;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        ">
          <div style="
            background: #f1f3f4;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-size: 14px;
          ">
            Welcome to our store! I'm here to help you find what you're looking for.
            \${config.customer.isLoggedIn ?
              \`<br><br>I can see you're logged in as \${config.customerData?.email || config.customer.email}. How can I assist you today?\` :
              '<br><br>Feel free to ask me any questions about our products!'
            }
            \${config.customerData ?
              \`<br><br><small>Customer ID: \${config.customerData.id}</small>\` :
              ''
            }
          </div>
        </div>
        
        <div style="
          padding: 16px;
          border-top: 1px solid #e0e0e0;
        ">
          <div style="display: flex; gap: 8px;">
            <input 
              type="text" 
              id="chat-input" 
              placeholder="Type your message..."
              style="
                flex: 1;
                padding: 12px;
                border: 1px solid #ddd;
                border-radius: 20px;
                outline: none;
                font-size: 14px;
              "
            />
            <button 
              id="send-button"
              style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    \`;
    
    document.body.appendChild(chatbotContainer);
    
    // Add event listeners
    setupEventListeners(config);
  }
  
  function setupEventListeners(config) {
    const widget = document.getElementById('chatbot-widget');
    const panel = document.getElementById('chatbot-panel');
    const input = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-button');
    
    // Toggle chat panel
    widget.addEventListener('click', () => {
      const isVisible = panel.style.display === 'flex';
      panel.style.display = isVisible ? 'none' : 'flex';

      if (!isVisible) {
        input.focus();

        // Update last seen when chatbot is opened
        if (config.customerId && config.shop) {
          updateLastSeen(config.customerId, config.shop.domain);
        }
      }
    });
    
    // Send message functionality
    function sendMessage() {
      const message = input.value.trim();
      if (!message) return;
      
      addMessage(message, 'user');
      input.value = '';
      
      // Simulate bot response
      setTimeout(() => {
        const responses = [
          "Thanks for your message! I'm a demo chatbot.",
          "I can help you with product information and support.",
          "Feel free to browse our products while we chat!",
          \`I can see you're on the page: \${config.page.title}\`,
          config.customer.isLoggedIn ? 
            \`Hi \${config.customer.firstName}, how can I help you today?\` : 
            "Would you like to create an account for a better experience?"
        ];
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        addMessage(randomResponse, 'bot');
      }, 1000);
    }
    
    sendButton.addEventListener('click', sendMessage);
    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        sendMessage();
      }
    });
  }
  
  function addMessage(text, sender) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    
    messageDiv.style.cssText = \`
      margin-bottom: 12px;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 14px;
      max-width: 80%;
      \${sender === 'user' ? 
        'background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; margin-left: auto; text-align: right;' : 
        'background: #f1f3f4; color: #333;'
      }
    \`;
    
    messageDiv.textContent = text;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }
  
  console.log('🤖 Chatbot embed script loaded successfully');
})();
  `.trim();
  return new Response(script, {
    headers: {
      "Content-Type": "application/javascript",
      "Cache-Control": "public, max-age=3600"
    }
  });
}
const route8 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$7
}, Symbol.toStringTag, { value: "Module" }));
const Polaris = /* @__PURE__ */ JSON.parse('{"ActionMenu":{"Actions":{"moreActions":"More actions"},"RollupActions":{"rollupButton":"View actions"}},"ActionList":{"SearchField":{"clearButtonLabel":"Clear","search":"Search","placeholder":"Search actions"}},"Avatar":{"label":"Avatar","labelWithInitials":"Avatar with initials {initials}"},"Autocomplete":{"spinnerAccessibilityLabel":"Loading","ellipsis":"{content}…"},"Badge":{"PROGRESS_LABELS":{"incomplete":"Incomplete","partiallyComplete":"Partially complete","complete":"Complete"},"TONE_LABELS":{"info":"Info","success":"Success","warning":"Warning","critical":"Critical","attention":"Attention","new":"New","readOnly":"Read-only","enabled":"Enabled"},"progressAndTone":"{toneLabel} {progressLabel}"},"Banner":{"dismissButton":"Dismiss notification"},"Button":{"spinnerAccessibilityLabel":"Loading"},"Common":{"checkbox":"checkbox","undo":"Undo","cancel":"Cancel","clear":"Clear","close":"Close","submit":"Submit","more":"More"},"ContextualSaveBar":{"save":"Save","discard":"Discard"},"DataTable":{"sortAccessibilityLabel":"sort {direction} by","navAccessibilityLabel":"Scroll table {direction} one column","totalsRowHeading":"Totals","totalRowHeading":"Total"},"DatePicker":{"previousMonth":"Show previous month, {previousMonthName} {showPreviousYear}","nextMonth":"Show next month, {nextMonth} {nextYear}","today":"Today ","start":"Start of range","end":"End of range","months":{"january":"January","february":"February","march":"March","april":"April","may":"May","june":"June","july":"July","august":"August","september":"September","october":"October","november":"November","december":"December"},"days":{"monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday","sunday":"Sunday"},"daysAbbreviated":{"monday":"Mo","tuesday":"Tu","wednesday":"We","thursday":"Th","friday":"Fr","saturday":"Sa","sunday":"Su"}},"DiscardConfirmationModal":{"title":"Discard all unsaved changes","message":"If you discard changes, you’ll delete any edits you made since you last saved.","primaryAction":"Discard changes","secondaryAction":"Continue editing"},"DropZone":{"single":{"overlayTextFile":"Drop file to upload","overlayTextImage":"Drop image to upload","overlayTextVideo":"Drop video to upload","actionTitleFile":"Add file","actionTitleImage":"Add image","actionTitleVideo":"Add video","actionHintFile":"or drop file to upload","actionHintImage":"or drop image to upload","actionHintVideo":"or drop video to upload","labelFile":"Upload file","labelImage":"Upload image","labelVideo":"Upload video"},"allowMultiple":{"overlayTextFile":"Drop files to upload","overlayTextImage":"Drop images to upload","overlayTextVideo":"Drop videos to upload","actionTitleFile":"Add files","actionTitleImage":"Add images","actionTitleVideo":"Add videos","actionHintFile":"or drop files to upload","actionHintImage":"or drop images to upload","actionHintVideo":"or drop videos to upload","labelFile":"Upload files","labelImage":"Upload images","labelVideo":"Upload videos"},"errorOverlayTextFile":"File type is not valid","errorOverlayTextImage":"Image type is not valid","errorOverlayTextVideo":"Video type is not valid"},"EmptySearchResult":{"altText":"Empty search results"},"Frame":{"skipToContent":"Skip to content","navigationLabel":"Navigation","Navigation":{"closeMobileNavigationLabel":"Close navigation"}},"FullscreenBar":{"back":"Back","accessibilityLabel":"Exit fullscreen mode"},"Filters":{"moreFilters":"More filters","moreFiltersWithCount":"More filters ({count})","filter":"Filter {resourceName}","noFiltersApplied":"No filters applied","cancel":"Cancel","done":"Done","clearAllFilters":"Clear all filters","clear":"Clear","clearLabel":"Clear {filterName}","addFilter":"Add filter","clearFilters":"Clear all","searchInView":"in:{viewName}"},"FilterPill":{"clear":"Clear","unsavedChanges":"Unsaved changes - {label}"},"IndexFilters":{"searchFilterTooltip":"Search and filter","searchFilterTooltipWithShortcut":"Search and filter (F)","searchFilterAccessibilityLabel":"Search and filter results","sort":"Sort your results","addView":"Add a new view","newView":"Custom search","SortButton":{"ariaLabel":"Sort the results","tooltip":"Sort","title":"Sort by","sorting":{"asc":"Ascending","desc":"Descending","az":"A-Z","za":"Z-A"}},"EditColumnsButton":{"tooltip":"Edit columns","accessibilityLabel":"Customize table column order and visibility"},"UpdateButtons":{"cancel":"Cancel","update":"Update","save":"Save","saveAs":"Save as","modal":{"title":"Save view as","label":"Name","sameName":"A view with this name already exists. Please choose a different name.","save":"Save","cancel":"Cancel"}}},"IndexProvider":{"defaultItemSingular":"Item","defaultItemPlural":"Items","allItemsSelected":"All {itemsLength}+ {resourceNamePlural} are selected","selected":"{selectedItemsCount} selected","a11yCheckboxDeselectAllSingle":"Deselect {resourceNameSingular}","a11yCheckboxSelectAllSingle":"Select {resourceNameSingular}","a11yCheckboxDeselectAllMultiple":"Deselect all {itemsLength} {resourceNamePlural}","a11yCheckboxSelectAllMultiple":"Select all {itemsLength} {resourceNamePlural}"},"IndexTable":{"emptySearchTitle":"No {resourceNamePlural} found","emptySearchDescription":"Try changing the filters or search term","onboardingBadgeText":"New","resourceLoadingAccessibilityLabel":"Loading {resourceNamePlural}…","selectAllLabel":"Select all {resourceNamePlural}","selected":"{selectedItemsCount} selected","undo":"Undo","selectAllItems":"Select all {itemsLength}+ {resourceNamePlural}","selectItem":"Select {resourceName}","selectButtonText":"Select","sortAccessibilityLabel":"sort {direction} by"},"Loading":{"label":"Page loading bar"},"Modal":{"iFrameTitle":"body markup","modalWarning":"These required properties are missing from Modal: {missingProps}"},"Page":{"Header":{"rollupActionsLabel":"View actions for {title}","pageReadyAccessibilityLabel":"{title}. This page is ready"}},"Pagination":{"previous":"Previous","next":"Next","pagination":"Pagination"},"ProgressBar":{"negativeWarningMessage":"Values passed to the progress prop shouldn’t be negative. Resetting {progress} to 0.","exceedWarningMessage":"Values passed to the progress prop shouldn’t exceed 100. Setting {progress} to 100."},"ResourceList":{"sortingLabel":"Sort by","defaultItemSingular":"item","defaultItemPlural":"items","showing":"Showing {itemsCount} {resource}","showingTotalCount":"Showing {itemsCount} of {totalItemsCount} {resource}","loading":"Loading {resource}","selected":"{selectedItemsCount} selected","allItemsSelected":"All {itemsLength}+ {resourceNamePlural} in your store are selected","allFilteredItemsSelected":"All {itemsLength}+ {resourceNamePlural} in this filter are selected","selectAllItems":"Select all {itemsLength}+ {resourceNamePlural} in your store","selectAllFilteredItems":"Select all {itemsLength}+ {resourceNamePlural} in this filter","emptySearchResultTitle":"No {resourceNamePlural} found","emptySearchResultDescription":"Try changing the filters or search term","selectButtonText":"Select","a11yCheckboxDeselectAllSingle":"Deselect {resourceNameSingular}","a11yCheckboxSelectAllSingle":"Select {resourceNameSingular}","a11yCheckboxDeselectAllMultiple":"Deselect all {itemsLength} {resourceNamePlural}","a11yCheckboxSelectAllMultiple":"Select all {itemsLength} {resourceNamePlural}","Item":{"actionsDropdownLabel":"Actions for {accessibilityLabel}","actionsDropdown":"Actions dropdown","viewItem":"View details for {itemName}"},"BulkActions":{"actionsActivatorLabel":"Actions","moreActionsActivatorLabel":"More actions"}},"SkeletonPage":{"loadingLabel":"Page loading"},"Tabs":{"newViewAccessibilityLabel":"Create new view","newViewTooltip":"Create view","toggleTabsLabel":"More views","Tab":{"rename":"Rename view","duplicate":"Duplicate view","edit":"Edit view","editColumns":"Edit columns","delete":"Delete view","copy":"Copy of {name}","deleteModal":{"title":"Delete view?","description":"This can’t be undone. {viewName} view will no longer be available in your admin.","cancel":"Cancel","delete":"Delete view"}},"RenameModal":{"title":"Rename view","label":"Name","cancel":"Cancel","create":"Save","errors":{"sameName":"A view with this name already exists. Please choose a different name."}},"DuplicateModal":{"title":"Duplicate view","label":"Name","cancel":"Cancel","create":"Create view","errors":{"sameName":"A view with this name already exists. Please choose a different name."}},"CreateViewModal":{"title":"Create new view","label":"Name","cancel":"Cancel","create":"Create view","errors":{"sameName":"A view with this name already exists. Please choose a different name."}}},"Tag":{"ariaLabel":"Remove {children}"},"TextField":{"characterCount":"{count} characters","characterCountWithMaxLength":"{count} of {limit} characters used"},"TooltipOverlay":{"accessibilityLabel":"Tooltip: {label}"},"TopBar":{"toggleMenuLabel":"Toggle menu","SearchField":{"clearButtonLabel":"Clear","search":"Search"}},"MediaCard":{"dismissButton":"Dismiss","popoverButton":"Actions"},"VideoThumbnail":{"playButtonA11yLabel":{"default":"Play video","defaultWithDuration":"Play video of length {duration}","duration":{"hours":{"other":{"only":"{hourCount} hours","andMinutes":"{hourCount} hours and {minuteCount} minutes","andMinute":"{hourCount} hours and {minuteCount} minute","minutesAndSeconds":"{hourCount} hours, {minuteCount} minutes, and {secondCount} seconds","minutesAndSecond":"{hourCount} hours, {minuteCount} minutes, and {secondCount} second","minuteAndSeconds":"{hourCount} hours, {minuteCount} minute, and {secondCount} seconds","minuteAndSecond":"{hourCount} hours, {minuteCount} minute, and {secondCount} second","andSeconds":"{hourCount} hours and {secondCount} seconds","andSecond":"{hourCount} hours and {secondCount} second"},"one":{"only":"{hourCount} hour","andMinutes":"{hourCount} hour and {minuteCount} minutes","andMinute":"{hourCount} hour and {minuteCount} minute","minutesAndSeconds":"{hourCount} hour, {minuteCount} minutes, and {secondCount} seconds","minutesAndSecond":"{hourCount} hour, {minuteCount} minutes, and {secondCount} second","minuteAndSeconds":"{hourCount} hour, {minuteCount} minute, and {secondCount} seconds","minuteAndSecond":"{hourCount} hour, {minuteCount} minute, and {secondCount} second","andSeconds":"{hourCount} hour and {secondCount} seconds","andSecond":"{hourCount} hour and {secondCount} second"}},"minutes":{"other":{"only":"{minuteCount} minutes","andSeconds":"{minuteCount} minutes and {secondCount} seconds","andSecond":"{minuteCount} minutes and {secondCount} second"},"one":{"only":"{minuteCount} minute","andSeconds":"{minuteCount} minute and {secondCount} seconds","andSecond":"{minuteCount} minute and {secondCount} second"}},"seconds":{"other":"{secondCount} seconds","one":"{secondCount} second"}}}}}');
const polarisTranslations = {
  Polaris
};
const polarisStyles = "/assets/styles-BeiPL2RV.css";
function loginErrorMessage(loginErrors) {
  if ((loginErrors == null ? void 0 : loginErrors.shop) === LoginErrorType.MissingShop) {
    return { shop: "Please enter your shop domain to log in" };
  } else if ((loginErrors == null ? void 0 : loginErrors.shop) === LoginErrorType.InvalidShop) {
    return { shop: "Please enter a valid shop domain to log in" };
  }
  return {};
}
const links$1 = () => [{ rel: "stylesheet", href: polarisStyles }];
const loader$6 = async ({ request }) => {
  const errors = loginErrorMessage(await login(request));
  return { errors, polarisTranslations };
};
const action$2 = async ({ request }) => {
  const errors = loginErrorMessage(await login(request));
  return {
    errors
  };
};
function Auth() {
  const loaderData = useLoaderData();
  const actionData = useActionData();
  const [shop, setShop] = useState("");
  const { errors } = actionData || loaderData;
  return /* @__PURE__ */ jsx(AppProvider, { i18n: loaderData.polarisTranslations, children: /* @__PURE__ */ jsx(Page, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsx(Form, { method: "post", children: /* @__PURE__ */ jsxs(FormLayout, { children: [
    /* @__PURE__ */ jsx(Text, { variant: "headingMd", as: "h2", children: "Log in" }),
    /* @__PURE__ */ jsx(
      TextField,
      {
        type: "text",
        name: "shop",
        label: "Shop domain",
        helpText: "example.myshopify.com",
        value: shop,
        onChange: setShop,
        autoComplete: "on",
        error: errors.shop
      }
    ),
    /* @__PURE__ */ jsx(Button, { submit: true, children: "Log in" })
  ] }) }) }) }) });
}
const route9 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$2,
  default: Auth,
  links: links$1,
  loader: loader$6
}, Symbol.toStringTag, { value: "Module" }));
async function loader$5({ request }) {
  try {
    const { session } = await authenticate.admin(request);
    if (!session) {
      throw new Response("No session found", { status: 400 });
    }
    return redirect(`/install?shop=${session.shop}`);
  } catch (error) {
    console.error("Error in callback:", error);
    return redirect("/auth/login");
  }
}
const route10 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$5
}, Symbol.toStringTag, { value: "Module" }));
async function createScriptTag(admin, src, displayScope = "ONLINE_STORE") {
  var _a2, _b, _c, _d, _e;
  const mutation = `
    mutation ScriptTagCreate($input: ScriptTagInput!) {
      scriptTagCreate(input: $input) {
        scriptTag {
          id
          cache
          createdAt
          displayScope
          src
          updatedAt
        }
        userErrors {
          field
          message
        }
      }
    }
  `;
  const variables = {
    input: {
      src,
      displayScope,
      cache: true
    }
  };
  const response = await admin.graphql(mutation, { variables });
  const responseJson = await response.json();
  if (((_c = (_b = (_a2 = responseJson.data) == null ? void 0 : _a2.scriptTagCreate) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
    throw new Error(`Script tag creation failed: ${responseJson.data.scriptTagCreate.userErrors.map((e) => e.message).join(", ")}`);
  }
  return (_e = (_d = responseJson.data) == null ? void 0 : _d.scriptTagCreate) == null ? void 0 : _e.scriptTag;
}
async function getScriptTags(admin) {
  var _a2, _b;
  const query = `
    query {
      scriptTags(first: 250) {
        edges {
          node {
            id
            src
            displayScope
            cache
            createdAt
            updatedAt
          }
        }
      }
    }
  `;
  const response = await admin.graphql(query);
  const responseJson = await response.json();
  if ((_b = (_a2 = responseJson.data) == null ? void 0 : _a2.scriptTags) == null ? void 0 : _b.edges) {
    return responseJson.data.scriptTags.edges.map((edge) => ({
      id: edge.node.id.replace("gid://shopify/ScriptTag/", ""),
      src: edge.node.src,
      display_scope: edge.node.displayScope,
      cache: edge.node.cache,
      created_at: edge.node.createdAt,
      updated_at: edge.node.updatedAt
    }));
  }
  return [];
}
async function findScriptTagsBySrc(admin, src) {
  const scriptTags = await getScriptTags(admin);
  return scriptTags.filter((tag) => tag.src === src);
}
async function deleteScriptTag(admin, scriptTagId) {
  var _a2, _b, _c;
  const mutation = `
    mutation ScriptTagDelete($id: ID!) {
      scriptTagDelete(id: $id) {
        deletedScriptTagId
        userErrors {
          field
          message
        }
      }
    }
  `;
  const variables = {
    id: `gid://shopify/ScriptTag/${scriptTagId}`
  };
  const response = await admin.graphql(mutation, { variables });
  const responseJson = await response.json();
  if (((_c = (_b = (_a2 = responseJson.data) == null ? void 0 : _a2.scriptTagDelete) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
    throw new Error(`Script tag deletion failed: ${responseJson.data.scriptTagDelete.userErrors.map((e) => e.message).join(", ")}`);
  }
}
async function removeScriptTagsBySrc(admin, src) {
  const scriptTags = await findScriptTagsBySrc(admin, src);
  for (const scriptTag of scriptTags) {
    await deleteScriptTag(admin, scriptTag.id);
  }
  return scriptTags.length;
}
async function ensureScriptTag(admin, src, displayScope = "ONLINE_STORE") {
  const existingTags = await findScriptTagsBySrc(admin, src);
  if (existingTags.length > 0) {
    return existingTags[0];
  }
  return await createScriptTag(admin, src, displayScope);
}
function getChatbotScriptUrl(appUrl) {
  const baseUrl = appUrl.replace(/\/$/, "");
  return `${baseUrl}/chatbot-loader.js`;
}
async function loader$4({ request }) {
  const url = new URL(request.url);
  const shop = url.searchParams.get("shop");
  if (!shop) {
    throw new Response("Shop parameter is required", { status: 400 });
  }
  try {
    const { session } = await authenticate.admin(request);
    if (session) {
      const appUrl = process.env.SHOPIFY_APP_URL;
      if (appUrl) {
        const scriptUrl = getChatbotScriptUrl(appUrl);
        const scriptTags = await getScriptTags(session);
        const chatbotScriptTag = scriptTags.find((tag) => tag.src === scriptUrl);
        return Response.json({
          shop,
          authenticated: true,
          scriptTagInstalled: !!chatbotScriptTag,
          scriptTag: chatbotScriptTag,
          scriptUrl
        });
      }
    }
  } catch (error) {
    console.log("Authentication failed in install route:", error.message);
  }
  return Response.json({
    shop,
    authenticated: false,
    scriptTagInstalled: false,
    scriptTag: null,
    scriptUrl: null
  });
}
async function action$1({ request }) {
  try {
    const { session } = await authenticate.admin(request);
    const formData = await request.formData();
    const action2 = formData.get("action");
    if (action2 === "install_script") {
      const appUrl = process.env.SHOPIFY_APP_URL;
      if (!appUrl) {
        return Response.json({ error: "App URL not configured" }, { status: 500 });
      }
      const scriptUrl = getChatbotScriptUrl(appUrl);
      const scriptTag = await ensureScriptTag(session, scriptUrl, "online_store");
      return Response.json({
        success: true,
        message: "Chatbot script tag installed successfully",
        scriptTag
      });
    }
    return Response.json({ error: "Invalid action" }, { status: 400 });
  } catch (error) {
    console.error("Error in install action:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
}
function Install() {
  var _a2, _b;
  const data = useLoaderData();
  const actionData = useActionData();
  return /* @__PURE__ */ jsxs("div", { style: { padding: "20px", maxWidth: "600px", margin: "0 auto" }, children: [
    /* @__PURE__ */ jsx("h1", { children: "🤖 Chatbot App Installation" }),
    /* @__PURE__ */ jsxs("div", { style: { marginBottom: "20px", padding: "15px", backgroundColor: "#f0f8ff", border: "1px solid #0066cc", borderRadius: "5px" }, children: [
      /* @__PURE__ */ jsxs("h2", { children: [
        "✅ App installed on ",
        data.shop
      ] }),
      /* @__PURE__ */ jsx("p", { children: "Your chatbot app has been successfully installed!" })
    ] }),
    data.authenticated ? /* @__PURE__ */ jsxs("div", { children: [
      /* @__PURE__ */ jsx("h3", { children: "Script Tag Status" }),
      data.scriptTagInstalled ? /* @__PURE__ */ jsxs("div", { style: { padding: "10px", backgroundColor: "#d4edda", border: "1px solid #c3e6cb", borderRadius: "5px", marginBottom: "15px" }, children: [
        /* @__PURE__ */ jsxs("p", { children: [
          "✅ ",
          /* @__PURE__ */ jsx("strong", { children: "Chatbot script tag is installed and active" })
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          "Script URL: ",
          /* @__PURE__ */ jsx("code", { children: data.scriptUrl })
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          "Script Tag ID: ",
          (_a2 = data.scriptTag) == null ? void 0 : _a2.id
        ] }),
        /* @__PURE__ */ jsxs("p", { children: [
          "Display Scope: ",
          (_b = data.scriptTag) == null ? void 0 : _b.display_scope
        ] })
      ] }) : /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsxs("div", { style: { padding: "10px", backgroundColor: "#fff3cd", border: "1px solid #ffeaa7", borderRadius: "5px", marginBottom: "15px" }, children: [
          /* @__PURE__ */ jsxs("p", { children: [
            "⚠️ ",
            /* @__PURE__ */ jsx("strong", { children: "Chatbot script tag is not installed" })
          ] }),
          /* @__PURE__ */ jsx("p", { children: "The script tag needs to be installed for the chatbot to appear on your storefront." })
        ] }),
        /* @__PURE__ */ jsxs(Form, { method: "post", children: [
          /* @__PURE__ */ jsx("input", { type: "hidden", name: "action", value: "install_script" }),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "submit",
              style: {
                padding: "10px 20px",
                backgroundColor: "#0066cc",
                color: "white",
                border: "none",
                borderRadius: "5px",
                cursor: "pointer",
                fontSize: "16px"
              },
              children: "Install Chatbot Script Tag"
            }
          )
        ] })
      ] }),
      actionData && /* @__PURE__ */ jsx("div", { style: {
        marginTop: "15px",
        padding: "10px",
        backgroundColor: actionData.success ? "#d4edda" : "#f8d7da",
        border: `1px solid ${actionData.success ? "#c3e6cb" : "#f5c6cb"}`,
        borderRadius: "5px"
      }, children: /* @__PURE__ */ jsxs("p", { children: [
        actionData.success ? "✅" : "❌",
        " ",
        actionData.message || actionData.error
      ] }) })
    ] }) : /* @__PURE__ */ jsxs("div", { style: { padding: "10px", backgroundColor: "#f8d7da", border: "1px solid #f5c6cb", borderRadius: "5px" }, children: [
      /* @__PURE__ */ jsxs("p", { children: [
        "⚠️ ",
        /* @__PURE__ */ jsx("strong", { children: "Authentication required" })
      ] }),
      /* @__PURE__ */ jsx("p", { children: "Please complete the app installation process to manage script tags." })
    ] }),
    /* @__PURE__ */ jsxs("div", { style: { marginTop: "30px", padding: "15px", backgroundColor: "#f8f9fa", border: "1px solid #dee2e6", borderRadius: "5px" }, children: [
      /* @__PURE__ */ jsx("h3", { children: "What happens next?" }),
      /* @__PURE__ */ jsxs("ul", { children: [
        /* @__PURE__ */ jsx("li", { children: "The chatbot script will be automatically loaded on your storefront" }),
        /* @__PURE__ */ jsx("li", { children: "Customer information (if logged in) will be passed to the chatbot" }),
        /* @__PURE__ */ jsx("li", { children: "The chatbot will appear according to your configuration" }),
        /* @__PURE__ */ jsx("li", { children: "Script tags are automatically managed during app installation/uninstallation" })
      ] })
    ] }),
    /* @__PURE__ */ jsx("div", { style: { marginTop: "20px", fontSize: "14px", color: "#666" }, children: /* @__PURE__ */ jsxs("p", { children: [
      /* @__PURE__ */ jsx("strong", { children: "Note:" }),
      " If you're seeing this page, the app installation webhook should have automatically created the script tag. If not, you can manually install it using the button above."
    ] }) })
  ] });
}
const route11 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$1,
  default: Install,
  loader: loader$4
}, Symbol.toStringTag, { value: "Module" }));
const route12 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null
}, Symbol.toStringTag, { value: "Module" }));
const loader$3 = async ({ request }) => {
  await authenticate.admin(request);
  return null;
};
const route13 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$3
}, Symbol.toStringTag, { value: "Module" }));
const index = "_index_1hqgz_1";
const heading = "_heading_1hqgz_21";
const text = "_text_1hqgz_23";
const content = "_content_1hqgz_43";
const form = "_form_1hqgz_53";
const label = "_label_1hqgz_69";
const input = "_input_1hqgz_85";
const button = "_button_1hqgz_93";
const list = "_list_1hqgz_101";
const styles = {
  index,
  heading,
  text,
  content,
  form,
  label,
  input,
  button,
  list
};
const loader$2 = async ({ request }) => {
  const url = new URL(request.url);
  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }
  return { showForm: Boolean(login) };
};
function App$1() {
  const { showForm } = useLoaderData();
  return /* @__PURE__ */ jsx("div", { className: styles.index, children: /* @__PURE__ */ jsxs("div", { className: styles.content, children: [
    /* @__PURE__ */ jsx("h1", { className: styles.heading, children: "A short heading about [your app]" }),
    /* @__PURE__ */ jsx("p", { className: styles.text, children: "A tagline about [your app] that describes your value proposition." }),
    showForm && /* @__PURE__ */ jsxs(Form, { className: styles.form, method: "post", action: "/auth/login", children: [
      /* @__PURE__ */ jsxs("label", { className: styles.label, children: [
        /* @__PURE__ */ jsx("span", { children: "Shop domain" }),
        /* @__PURE__ */ jsx("input", { className: styles.input, type: "text", name: "shop" }),
        /* @__PURE__ */ jsx("span", { children: "e.g: my-shop-domain.myshopify.com" })
      ] }),
      /* @__PURE__ */ jsx("button", { className: styles.button, type: "submit", children: "Log in" })
    ] }),
    /* @__PURE__ */ jsxs("ul", { className: styles.list, children: [
      /* @__PURE__ */ jsxs("li", { children: [
        /* @__PURE__ */ jsx("strong", { children: "Product feature" }),
        ". Some detail about your feature and its benefit to your customer."
      ] }),
      /* @__PURE__ */ jsxs("li", { children: [
        /* @__PURE__ */ jsx("strong", { children: "Product feature" }),
        ". Some detail about your feature and its benefit to your customer."
      ] }),
      /* @__PURE__ */ jsxs("li", { children: [
        /* @__PURE__ */ jsx("strong", { children: "Product feature" }),
        ". Some detail about your feature and its benefit to your customer."
      ] })
    ] })
  ] }) });
}
const route14 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: App$1,
  loader: loader$2
}, Symbol.toStringTag, { value: "Module" }));
const links = () => [{ rel: "stylesheet", href: polarisStyles }];
const loader$1 = async ({ request }) => {
  await authenticate.admin(request);
  return { apiKey: process.env.SHOPIFY_API_KEY || "" };
};
function App() {
  const { apiKey } = useLoaderData();
  return /* @__PURE__ */ jsxs(AppProvider$1, { isEmbeddedApp: true, apiKey, children: [
    /* @__PURE__ */ jsxs(NavMenu, { children: [
      /* @__PURE__ */ jsx(Link, { to: "/app", rel: "home", children: "Home" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/additional", children: "Additional page" })
    ] }),
    /* @__PURE__ */ jsx(Outlet, {})
  ] });
}
function ErrorBoundary() {
  return boundary.error(useRouteError());
}
const headers = (headersArgs) => {
  return boundary.headers(headersArgs);
};
const route15 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  ErrorBoundary,
  default: App,
  headers,
  links,
  loader: loader$1
}, Symbol.toStringTag, { value: "Module" }));
function AdditionalPage() {
  return /* @__PURE__ */ jsxs(Page, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "Additional page" }),
    /* @__PURE__ */ jsxs(Layout, { children: [
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "300", children: [
        /* @__PURE__ */ jsxs(Text, { as: "p", variant: "bodyMd", children: [
          "The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using",
          " ",
          /* @__PURE__ */ jsx(
            Link$1,
            {
              url: "https://shopify.dev/docs/apps/tools/app-bridge",
              target: "_blank",
              removeUnderline: true,
              children: "App Bridge"
            }
          ),
          "."
        ] }),
        /* @__PURE__ */ jsxs(Text, { as: "p", variant: "bodyMd", children: [
          "To create your own page and have it show up in the app navigation, add a page inside ",
          /* @__PURE__ */ jsx(Code, { children: "app/routes" }),
          ", and a link to it in the ",
          /* @__PURE__ */ jsx(Code, { children: "<NavMenu>" }),
          " component found in ",
          /* @__PURE__ */ jsx(Code, { children: "app/routes/app.jsx" }),
          "."
        ] })
      ] }) }) }),
      /* @__PURE__ */ jsx(Layout.Section, { variant: "oneThird", children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
        /* @__PURE__ */ jsx(Text, { as: "h2", variant: "headingMd", children: "Resources" }),
        /* @__PURE__ */ jsx(List, { children: /* @__PURE__ */ jsx(List.Item, { children: /* @__PURE__ */ jsx(
          Link$1,
          {
            url: "https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",
            target: "_blank",
            removeUnderline: true,
            children: "App nav best practices"
          }
        ) }) })
      ] }) }) })
    ] })
  ] });
}
function Code({ children }) {
  return /* @__PURE__ */ jsx(
    Box,
    {
      as: "span",
      padding: "025",
      paddingInlineStart: "100",
      paddingInlineEnd: "100",
      background: "bg-surface-active",
      borderWidth: "025",
      borderColor: "border",
      borderRadius: "100",
      children: /* @__PURE__ */ jsx("code", { children })
    }
  );
}
const route16 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: AdditionalPage
}, Symbol.toStringTag, { value: "Module" }));
const loader = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  try {
    const scriptTags = await getScriptTags(admin);
    const appUrl = process.env.SHOPIFY_APP_URL;
    const chatbotScriptUrl = appUrl ? getChatbotScriptUrl(appUrl) : null;
    const chatbotScriptTag = scriptTags.find(
      (tag) => chatbotScriptUrl && tag.src === chatbotScriptUrl
    );
    return Response.json({
      shop: session.shop,
      scriptTags,
      chatbotScriptTag,
      chatbotScriptUrl,
      appUrl,
      isInstalled: !!chatbotScriptTag
    });
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return Response.json({
      shop: session.shop,
      scriptTags: [],
      chatbotScriptTag: null,
      chatbotScriptUrl: null,
      appUrl: process.env.SHOPIFY_APP_URL,
      isInstalled: false,
      error: error.message
    });
  }
};
const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const action2 = formData.get("action");
  try {
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (!appUrl) {
      return Response.json({ error: "App URL not configured" }, { status: 500 });
    }
    const scriptUrl = getChatbotScriptUrl(appUrl);
    switch (action2) {
      case "install_script":
        const scriptTag = await ensureScriptTag(admin, scriptUrl, "ONLINE_STORE");
        return Response.json({
          success: true,
          message: "Chatbot script tag installed successfully",
          scriptTag
        });
      case "uninstall_script":
        const removedCount = await removeScriptTagsBySrc(admin, scriptUrl);
        return Response.json({
          success: true,
          message: `Removed ${removedCount} chatbot script tag(s)`,
          removedCount
        });
      default:
        return Response.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Error in dashboard action:", error);
    return Response.json({ error: error.message }, { status: 500 });
  }
};
function ChatbotDashboard() {
  var _a2, _b, _c, _d, _e;
  const data = useLoaderData();
  const fetcher = useFetcher();
  const shopify2 = useAppBridge();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const isLoading = ["loading", "submitting"].includes(fetcher.state);
  useEffect(() => {
    var _a3, _b2;
    if ((_a3 = fetcher.data) == null ? void 0 : _a3.success) {
      shopify2.toast.show(fetcher.data.message);
      setIsRefreshing(true);
      setTimeout(() => {
        window.location.reload();
      }, 1e3);
    } else if ((_b2 = fetcher.data) == null ? void 0 : _b2.error) {
      shopify2.toast.show(fetcher.data.error, { isError: true });
    }
  }, [fetcher.data, shopify2]);
  const installScript = () => {
    fetcher.submit({ action: "install_script" }, { method: "POST" });
  };
  const uninstallScript = () => {
    fetcher.submit({ action: "uninstall_script" }, { method: "POST" });
  };
  return /* @__PURE__ */ jsxs(Page, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "Chatbot Dashboard" }),
    data.error && /* @__PURE__ */ jsx(Banner, { status: "critical", title: "Error loading dashboard", children: /* @__PURE__ */ jsx("p", { children: data.error }) }),
    /* @__PURE__ */ jsx(BlockStack, { gap: "500", children: /* @__PURE__ */ jsxs(Layout, { children: [
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "500", children: [
        /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
          /* @__PURE__ */ jsxs(Text, { as: "h2", variant: "headingMd", children: [
            "🤖 Chatbot Status for ",
            data.shop
          ] }),
          /* @__PURE__ */ jsxs(InlineStack, { gap: "200", align: "start", children: [
            /* @__PURE__ */ jsx(
              Badge,
              {
                status: data.isInstalled ? "success" : "attention",
                size: "medium",
                children: data.isInstalled ? "Active" : "Not Installed"
              }
            ),
            isRefreshing && /* @__PURE__ */ jsx(Spinner, { size: "small" })
          ] })
        ] }),
        data.isInstalled ? /* @__PURE__ */ jsxs(BlockStack, { gap: "300", children: [
          /* @__PURE__ */ jsx(Text, { variant: "bodyMd", as: "p", color: "success", children: "✅ Your chatbot is successfully installed and running on your storefront!" }),
          /* @__PURE__ */ jsx(
            Box,
            {
              padding: "400",
              background: "bg-surface-success",
              borderWidth: "025",
              borderRadius: "200",
              borderColor: "border-success",
              children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
                /* @__PURE__ */ jsx(Text, { as: "h4", variant: "headingSm", children: "Script Tag Details:" }),
                /* @__PURE__ */ jsxs(Text, { variant: "bodySm", children: [
                  /* @__PURE__ */ jsx("strong", { children: "Script URL:" }),
                  " ",
                  (_a2 = data.chatbotScriptTag) == null ? void 0 : _a2.src
                ] }),
                /* @__PURE__ */ jsxs(Text, { variant: "bodySm", children: [
                  /* @__PURE__ */ jsx("strong", { children: "Display Scope:" }),
                  " ",
                  ((_b = data.chatbotScriptTag) == null ? void 0 : _b.display_scope) || "online_store"
                ] }),
                /* @__PURE__ */ jsxs(Text, { variant: "bodySm", children: [
                  /* @__PURE__ */ jsx("strong", { children: "Created:" }),
                  " ",
                  ((_c = data.chatbotScriptTag) == null ? void 0 : _c.created_at) ? new Date(data.chatbotScriptTag.created_at).toLocaleString() : "Unknown"
                ] })
              ] })
            }
          ),
          /* @__PURE__ */ jsxs(InlineStack, { gap: "300", children: [
            /* @__PURE__ */ jsx(
              Button,
              {
                variant: "primary",
                tone: "critical",
                loading: isLoading,
                onClick: uninstallScript,
                children: "Uninstall Chatbot"
              }
            ),
            /* @__PURE__ */ jsx(
              Button,
              {
                url: `https://${data.shop}`,
                target: "_blank",
                variant: "plain",
                children: "View Storefront"
              }
            )
          ] })
        ] }) : /* @__PURE__ */ jsxs(BlockStack, { gap: "300", children: [
          /* @__PURE__ */ jsx(Text, { variant: "bodyMd", as: "p", children: "Your chatbot is not currently installed on your storefront. Click the button below to install it." }),
          /* @__PURE__ */ jsx(
            Button,
            {
              variant: "primary",
              loading: isLoading,
              onClick: installScript,
              children: "Install Chatbot"
            }
          )
        ] })
      ] }) }) }),
      /* @__PURE__ */ jsx(Layout.Section, { variant: "oneThird", children: /* @__PURE__ */ jsxs(BlockStack, { gap: "500", children: [
        /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
          /* @__PURE__ */ jsx(Text, { as: "h2", variant: "headingMd", children: "📊 Script Tags Overview" }),
          /* @__PURE__ */ jsxs(Text, { variant: "bodyMd", children: [
            "Total script tags: ",
            /* @__PURE__ */ jsx("strong", { children: ((_d = data.scriptTags) == null ? void 0 : _d.length) || 0 })
          ] }),
          ((_e = data.scriptTags) == null ? void 0 : _e.length) > 0 && /* @__PURE__ */ jsx(
            Box,
            {
              padding: "300",
              background: "bg-surface-secondary",
              borderWidth: "025",
              borderRadius: "200",
              borderColor: "border",
              children: /* @__PURE__ */ jsxs(BlockStack, { gap: "100", children: [
                data.scriptTags.slice(0, 5).map((tag, index2) => {
                  var _a3;
                  return /* @__PURE__ */ jsxs(Text, { variant: "bodySm", children: [
                    "• ",
                    ((_a3 = tag.src) == null ? void 0 : _a3.split("/").pop()) || "Unknown script"
                  ] }, index2);
                }),
                data.scriptTags.length > 5 && /* @__PURE__ */ jsxs(Text, { variant: "bodySm", color: "subdued", children: [
                  "... and ",
                  data.scriptTags.length - 5,
                  " more"
                ] })
              ] })
            }
          )
        ] }) }),
        /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
          /* @__PURE__ */ jsx(Text, { as: "h2", variant: "headingMd", children: "🔧 Configuration" }),
          /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
            /* @__PURE__ */ jsxs(InlineStack, { align: "space-between", children: [
              /* @__PURE__ */ jsx(Text, { as: "span", variant: "bodyMd", children: "App URL" }),
              /* @__PURE__ */ jsx(Text, { variant: "bodySm", color: "subdued", children: data.appUrl ? new URL(data.appUrl).hostname : "Not configured" })
            ] }),
            /* @__PURE__ */ jsxs(InlineStack, { align: "space-between", children: [
              /* @__PURE__ */ jsx(Text, { as: "span", variant: "bodyMd", children: "Script URL" }),
              /* @__PURE__ */ jsx(Text, { variant: "bodySm", color: "subdued", children: data.chatbotScriptUrl ? "/chatbot-loader.js" : "Not available" })
            ] }),
            /* @__PURE__ */ jsxs(InlineStack, { align: "space-between", children: [
              /* @__PURE__ */ jsx(Text, { as: "span", variant: "bodyMd", children: "Embed Script" }),
              /* @__PURE__ */ jsx(Text, { variant: "bodySm", color: "subdued", children: "/chatbot-embed.js" })
            ] })
          ] })
        ] }) }),
        /* @__PURE__ */ jsx(Card, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
          /* @__PURE__ */ jsx(Text, { as: "h2", variant: "headingMd", children: "📚 Features" }),
          /* @__PURE__ */ jsxs(List, { children: [
            /* @__PURE__ */ jsx(List.Item, { children: "Automatic customer identification" }),
            /* @__PURE__ */ jsx(List.Item, { children: "Real-time chat interface" }),
            /* @__PURE__ */ jsx(List.Item, { children: "Mobile-responsive design" }),
            /* @__PURE__ */ jsx(List.Item, { children: "Easy installation & removal" })
          ] })
        ] }) })
      ] }) })
    ] }) })
  ] });
}
const route17 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action,
  default: ChatbotDashboard,
  loader
}, Symbol.toStringTag, { value: "Module" }));
const serverManifest = { "entry": { "module": "/assets/entry.client-B4p3FXZU.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/components-qeoBKuxV.js"], "css": [] }, "routes": { "root": { "id": "root", "parentId": void 0, "path": "", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/root-BZn3ATJ0.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/components-qeoBKuxV.js"], "css": [] }, "routes/webhooks.app.scopes_update": { "id": "routes/webhooks.app.scopes_update", "parentId": "root", "path": "webhooks/app/scopes_update", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app.scopes_update-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.customers.create": { "id": "routes/webhooks.customers.create", "parentId": "root", "path": "webhooks/customers/create", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.customers.create-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.customers.update": { "id": "routes/webhooks.customers.update", "parentId": "root", "path": "webhooks/customers/update", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.customers.update-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/api.customer.$customerId": { "id": "routes/api.customer.$customerId", "parentId": "root", "path": "api/customer/:customerId", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/api.customer._customerId-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.app.uninstalled": { "id": "routes/webhooks.app.uninstalled", "parentId": "root", "path": "webhooks/app/uninstalled", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app.uninstalled-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/api.customer.track": { "id": "routes/api.customer.track", "parentId": "root", "path": "api/customer/track", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/api.customer.track-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/chatbot-loader": { "id": "routes/chatbot-loader", "parentId": "root", "path": "chatbot-loader", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/chatbot-loader-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/chatbot-embed": { "id": "routes/chatbot-embed", "parentId": "root", "path": "chatbot-embed", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/chatbot-embed-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/auth.login": { "id": "routes/auth.login", "parentId": "root", "path": "auth/login", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/route-CNoc41jw.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/styles-bRXb-Ils.js", "/assets/components-qeoBKuxV.js", "/assets/Page-BAvRnlvb.js", "/assets/context-BJzNQyky.js"], "css": [] }, "routes/callback": { "id": "routes/callback", "parentId": "root", "path": "callback", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/callback-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/install": { "id": "routes/install", "parentId": "root", "path": "install", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/install-afEcw40f.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/components-qeoBKuxV.js"], "css": [] }, "routes/webhook": { "id": "routes/webhook", "parentId": "root", "path": "webhook", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhook-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/auth.$": { "id": "routes/auth.$", "parentId": "root", "path": "auth/*", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/auth._-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/_index": { "id": "routes/_index", "parentId": "root", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/route-BDQf8MaJ.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/components-qeoBKuxV.js"], "css": ["/assets/route-Cnm7FvdT.css"] }, "routes/app": { "id": "routes/app", "parentId": "root", "path": "app", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": true, "module": "/assets/app-BnasjhWB.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/components-qeoBKuxV.js", "/assets/styles-bRXb-Ils.js", "/assets/context-BJzNQyky.js"], "css": [] }, "routes/app.additional": { "id": "routes/app.additional", "parentId": "routes/app", "path": "additional", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.additional-CzDoWRGl.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/Page-BAvRnlvb.js", "/assets/TitleBar-DiPv-53d.js", "/assets/context-BJzNQyky.js"], "css": [] }, "routes/app._index": { "id": "routes/app._index", "parentId": "routes/app", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app._index-CIUjNsmD.js", "imports": ["/assets/index-C0U6NBub.js", "/assets/components-qeoBKuxV.js", "/assets/Page-BAvRnlvb.js", "/assets/TitleBar-DiPv-53d.js", "/assets/context-BJzNQyky.js"], "css": [] } }, "url": "/assets/manifest-023f7359.js", "version": "023f7359" };
const mode = "production";
const assetsBuildDirectory = "build\\client";
const basename = "/";
const future = { "v3_fetcherPersist": true, "v3_relativeSplatPath": true, "v3_throwAbortReason": true, "v3_routeConfig": true, "v3_singleFetch": false, "v3_lazyRouteDiscovery": true, "unstable_optimizeDeps": false };
const isSpaMode = false;
const publicPath = "/";
const entry = { module: entryServer };
const routes = {
  "root": {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: route0
  },
  "routes/webhooks.app.scopes_update": {
    id: "routes/webhooks.app.scopes_update",
    parentId: "root",
    path: "webhooks/app/scopes_update",
    index: void 0,
    caseSensitive: void 0,
    module: route1
  },
  "routes/webhooks.customers.create": {
    id: "routes/webhooks.customers.create",
    parentId: "root",
    path: "webhooks/customers/create",
    index: void 0,
    caseSensitive: void 0,
    module: route2
  },
  "routes/webhooks.customers.update": {
    id: "routes/webhooks.customers.update",
    parentId: "root",
    path: "webhooks/customers/update",
    index: void 0,
    caseSensitive: void 0,
    module: route3
  },
  "routes/api.customer.$customerId": {
    id: "routes/api.customer.$customerId",
    parentId: "root",
    path: "api/customer/:customerId",
    index: void 0,
    caseSensitive: void 0,
    module: route4
  },
  "routes/webhooks.app.uninstalled": {
    id: "routes/webhooks.app.uninstalled",
    parentId: "root",
    path: "webhooks/app/uninstalled",
    index: void 0,
    caseSensitive: void 0,
    module: route5
  },
  "routes/api.customer.track": {
    id: "routes/api.customer.track",
    parentId: "root",
    path: "api/customer/track",
    index: void 0,
    caseSensitive: void 0,
    module: route6
  },
  "routes/chatbot-loader": {
    id: "routes/chatbot-loader",
    parentId: "root",
    path: "chatbot-loader",
    index: void 0,
    caseSensitive: void 0,
    module: route7
  },
  "routes/chatbot-embed": {
    id: "routes/chatbot-embed",
    parentId: "root",
    path: "chatbot-embed",
    index: void 0,
    caseSensitive: void 0,
    module: route8
  },
  "routes/auth.login": {
    id: "routes/auth.login",
    parentId: "root",
    path: "auth/login",
    index: void 0,
    caseSensitive: void 0,
    module: route9
  },
  "routes/callback": {
    id: "routes/callback",
    parentId: "root",
    path: "callback",
    index: void 0,
    caseSensitive: void 0,
    module: route10
  },
  "routes/install": {
    id: "routes/install",
    parentId: "root",
    path: "install",
    index: void 0,
    caseSensitive: void 0,
    module: route11
  },
  "routes/webhook": {
    id: "routes/webhook",
    parentId: "root",
    path: "webhook",
    index: void 0,
    caseSensitive: void 0,
    module: route12
  },
  "routes/auth.$": {
    id: "routes/auth.$",
    parentId: "root",
    path: "auth/*",
    index: void 0,
    caseSensitive: void 0,
    module: route13
  },
  "routes/_index": {
    id: "routes/_index",
    parentId: "root",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route14
  },
  "routes/app": {
    id: "routes/app",
    parentId: "root",
    path: "app",
    index: void 0,
    caseSensitive: void 0,
    module: route15
  },
  "routes/app.additional": {
    id: "routes/app.additional",
    parentId: "routes/app",
    path: "additional",
    index: void 0,
    caseSensitive: void 0,
    module: route16
  },
  "routes/app._index": {
    id: "routes/app._index",
    parentId: "routes/app",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route17
  }
};
export {
  serverManifest as assets,
  assetsBuildDirectory,
  basename,
  entry,
  future,
  isSpaMode,
  mode,
  publicPath,
  routes
};
