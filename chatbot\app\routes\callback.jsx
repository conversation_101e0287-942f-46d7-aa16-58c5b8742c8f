import { redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export async function loader({ request }) {
  try {
    const { session } = await authenticate.admin(request);

    if (!session) {
      throw new Response("No session found", { status: 400 });
    }

    return redirect(`/install?shop=${session.shop}`);

  } catch (error) {
    console.error("Error in callback:", error);
    return redirect("/auth/login");
  }
}