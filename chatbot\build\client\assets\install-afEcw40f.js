import{j as s}from"./index-C0U6NBub.js";import{u as a,a as o,F as d}from"./components-qeoBKuxV.js";function c(){var e,r;const i=a(),t=o();return s.jsxs("div",{style:{padding:"20px",maxWidth:"600px",margin:"0 auto"},children:[s.jsx("h1",{children:"🤖 Chatbot App Installation"}),s.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f0f8ff",border:"1px solid #0066cc",borderRadius:"5px"},children:[s.jsxs("h2",{children:["✅ App installed on ",i.shop]}),s.jsx("p",{children:"Your chatbot app has been successfully installed!"})]}),i.authenticated?s.jsxs("div",{children:[s.jsx("h3",{children:"Script Tag Status"}),i.scriptTagInstalled?s.jsxs("div",{style:{padding:"10px",backgroundColor:"#d4edda",border:"1px solid #c3e6cb",borderRadius:"5px",marginBottom:"15px"},children:[s.jsxs("p",{children:["✅ ",s.jsx("strong",{children:"Chatbot script tag is installed and active"})]}),s.jsxs("p",{children:["Script URL: ",s.jsx("code",{children:i.scriptUrl})]}),s.jsxs("p",{children:["Script Tag ID: ",(e=i.scriptTag)==null?void 0:e.id]}),s.jsxs("p",{children:["Display Scope: ",(r=i.scriptTag)==null?void 0:r.display_scope]})]}):s.jsxs("div",{children:[s.jsxs("div",{style:{padding:"10px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"5px",marginBottom:"15px"},children:[s.jsxs("p",{children:["⚠️ ",s.jsx("strong",{children:"Chatbot script tag is not installed"})]}),s.jsx("p",{children:"The script tag needs to be installed for the chatbot to appear on your storefront."})]}),s.jsxs(d,{method:"post",children:[s.jsx("input",{type:"hidden",name:"action",value:"install_script"}),s.jsx("button",{type:"submit",style:{padding:"10px 20px",backgroundColor:"#0066cc",color:"white",border:"none",borderRadius:"5px",cursor:"pointer",fontSize:"16px"},children:"Install Chatbot Script Tag"})]})]}),t&&s.jsx("div",{style:{marginTop:"15px",padding:"10px",backgroundColor:t.success?"#d4edda":"#f8d7da",border:`1px solid ${t.success?"#c3e6cb":"#f5c6cb"}`,borderRadius:"5px"},children:s.jsxs("p",{children:[t.success?"✅":"❌"," ",t.message||t.error]})})]}):s.jsxs("div",{style:{padding:"10px",backgroundColor:"#f8d7da",border:"1px solid #f5c6cb",borderRadius:"5px"},children:[s.jsxs("p",{children:["⚠️ ",s.jsx("strong",{children:"Authentication required"})]}),s.jsx("p",{children:"Please complete the app installation process to manage script tags."})]}),s.jsxs("div",{style:{marginTop:"30px",padding:"15px",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"5px"},children:[s.jsx("h3",{children:"What happens next?"}),s.jsxs("ul",{children:[s.jsx("li",{children:"The chatbot script will be automatically loaded on your storefront"}),s.jsx("li",{children:"Customer information (if logged in) will be passed to the chatbot"}),s.jsx("li",{children:"The chatbot will appear according to your configuration"}),s.jsx("li",{children:"Script tags are automatically managed during app installation/uninstallation"})]})]}),s.jsx("div",{style:{marginTop:"20px",fontSize:"14px",color:"#666"},children:s.jsxs("p",{children:[s.jsx("strong",{children:"Note:"})," If you're seeing this page, the app installation webhook should have automatically created the script tag. If not, you can manually install it using the button above."]})})]})}export{c as default};
