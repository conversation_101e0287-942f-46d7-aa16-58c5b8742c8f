import db from "../db.server";

export const action = async ({ request }) => {
  try {
    const formData = await request.formData();
    const customerId = formData.get("customerId");
    const shop = formData.get("shop");
    const email = formData.get("email");
    const firstName = formData.get("firstName");
    const lastName = formData.get("lastName");
    const action = formData.get("action");

    if (!customerId || !shop) {
      return Response.json({ error: "Customer ID and shop are required" }, { status: 400 });
    }

    try {
      switch (action) {
        case "track_customer":
          // Store or update customer data when they interact with chatbot
          await db.customer.upsert({
            where: {
              shopify_customer_id_shop: {
                shopify_customer_id: customerId,
                shop: shop
              }
            },
            update: {
              email: email || undefined,
              first_name: firstName || undefined,
              last_name: lastName || undefined,
              last_seen: new Date()
            },
            create: {
              shopify_customer_id: customerId,
              shop: shop,
              email: email || null,
              first_name: firstName || null,
              last_name: lastName || null,
              phone: null,
              accepts_marketing: false,
              created_at: new Date(),
              updated_at: new Date(),
              last_seen: new Date()
            }
          });

          console.log(`Customer ${customerId} tracked for shop ${shop}`);
          return Response.json({ success: true, message: "Customer tracked successfully" });

        case "update_last_seen":
          // Update last seen timestamp
          await db.customer.update({
            where: {
              shopify_customer_id_shop: {
                shopify_customer_id: customerId,
                shop: shop
              }
            },
            data: {
              last_seen: new Date()
            }
          });

          return Response.json({ success: true, message: "Last seen updated" });

        default:
          return Response.json({ error: "Invalid action" }, { status: 400 });
      }

    } catch (dbError) {
      console.error(`Failed to track customer data:`, dbError);
      return Response.json({ error: "Database error" }, { status: 500 });
    }

  } catch (error) {
    console.error("Error in customer tracking API:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
};
