import { authenticate } from "../shopify.server";
import db from "../db.server";

export const action = async ({ request }) => {
  try {
    const { shop, session, topic, payload } = await authenticate.webhook(request);

    console.log(`Received ${topic} webhook for ${shop}`);

    if (!payload) {
      console.error("No payload received for customer create webhook");
      return new Response("No payload", { status: 400 });
    }

    // Extract customer data from payload
    const customer = payload;
    
    try {
      // Store or update customer data in database
      await db.customer.upsert({
        where: {
          shopify_customer_id_shop: {
            shopify_customer_id: customer.id.toString(),
            shop: shop
          }
        },
        update: {
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          accepts_marketing: customer.accepts_marketing,
          created_at: new Date(customer.created_at),
          updated_at: new Date(customer.updated_at),
          last_seen: new Date()
        },
        create: {
          shopify_customer_id: customer.id.toString(),
          shop: shop,
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          phone: customer.phone,
          accepts_marketing: customer.accepts_marketing,
          created_at: new Date(customer.created_at),
          updated_at: new Date(customer.updated_at),
          last_seen: new Date()
        }
      });

      console.log(`Customer ${customer.id} data stored/updated for shop ${shop}`);

    } catch (dbError) {
      console.error(`Failed to store customer data for ${shop}:`, dbError);
      // Don't fail the webhook, just log the error
    }

    return new Response("OK", { status: 200 });

  } catch (error) {
    console.error("Error processing customer create webhook:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};
