# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "fc5ccb3037e1322db6915a4de00972c1"
name = "chatBot"
handle = "chatbot-252"
application_url = "https://depot-customs-facilitate-baltimore.trycloudflare.com"
embedded = true

[webhooks]
api_version = "2025-07"

[auth]
redirect_urls = ["https://depot-customs-facilitate-baltimore.trycloudflare.com/auth/callback", "https://depot-customs-facilitate-baltimore.trycloudflare.com/auth/shopify/callback", "https://depot-customs-facilitate-baltimore.trycloudflare.com/api/auth/callback"]

[pos]
embedded = false

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true
