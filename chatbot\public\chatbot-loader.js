(function () {
  // Prevent multiple initializations
  if (window.ChatbotLoaderInitialized) {
    return;
  }
  window.ChatbotLoaderInitialized = true;

  // Configuration
  const config = {
    // Replace with your actual chatbot embed script URL
    embedScriptUrl: "https://ohio-instrumental-spoken-towers.trycloudflare.com/chatbot-embed.js",
    // Timeout for script loading (in milliseconds)
    loadTimeout: 10000,
    // Retry attempts if script fails to load
    maxRetries: 3
  };

  let retryCount = 0;

  function loadChatbotScript() {
    const script = document.createElement("script");
    script.src = config.embedScriptUrl;
    script.async = true;
    script.defer = true;

    // Set up timeout
    const timeoutId = setTimeout(() => {
      console.warn("Chatbot script loading timed out");
      handleScriptError();
    }, config.loadTimeout);

    script.onload = function () {
      clearTimeout(timeoutId);
      initializeChatbot();
    };

    script.onerror = function () {
      clearTimeout(timeoutId);
      console.error("Failed to load chatbot script");
      handleScriptError();
    };

    document.head.appendChild(script);
  }

  function handleScriptError() {
    retryCount++;
    if (retryCount < config.maxRetries) {
      console.log(`Retrying chatbot script load (attempt ${retryCount + 1}/${config.maxRetries})`);
      setTimeout(loadChatbotScript, 2000 * retryCount); // Exponential backoff
    } else {
      console.error("Failed to load chatbot script after maximum retries");
    }
  }

  function initializeChatbot() {
    try {
      // Get customer information from multiple Shopify sources
      let customerId = null;
      let customerEmail = null;
      let customerFirstName = null;
      let customerLastName = null;

      // Method 1: Check Shopify.customer (most common)
      if (window.Shopify?.customer?.id) {
        customerId = window.Shopify.customer.id;
        customerEmail = window.Shopify.customer.email;
        customerFirstName = window.Shopify.customer.first_name;
        customerLastName = window.Shopify.customer.last_name;
      }
      // Method 2: Check for customer data in meta tags
      else {
        const customerIdMeta = document.querySelector('meta[name="shopify-customer-id"]');
        const customerEmailMeta = document.querySelector('meta[name="shopify-customer-email"]');
        const customerFirstNameMeta = document.querySelector('meta[name="shopify-customer-first-name"]');
        const customerLastNameMeta = document.querySelector('meta[name="shopify-customer-last-name"]');

        if (customerIdMeta?.content) {
          customerId = customerIdMeta.content;
          customerEmail = customerEmailMeta?.content || null;
          customerFirstName = customerFirstNameMeta?.content || null;
          customerLastName = customerLastNameMeta?.content || null;
        }
      }

      // Method 3: Check for customer data in data attributes on body or html
      if (!customerId) {
        const bodyCustomerId = document.body?.dataset?.customerId || document.documentElement?.dataset?.customerId;
        if (bodyCustomerId) {
          customerId = bodyCustomerId;
          customerEmail = document.body?.dataset?.customerEmail || document.documentElement?.dataset?.customerEmail || null;
          customerFirstName = document.body?.dataset?.customerFirstName || document.documentElement?.dataset?.customerFirstName || null;
          customerLastName = document.body?.dataset?.customerLastName || document.documentElement?.dataset?.customerLastName || null;
        }
      }

      // Get shop information
      const shopDomain = window.Shopify?.shop || window.location.hostname;

      // Prepare chatbot configuration
      const chatbotConfig = {
        customerId: customerId,
        customer: {
          id: customerId,
          email: customerEmail,
          firstName: customerFirstName,
          lastName: customerLastName,
          isLoggedIn: !!customerId
        },
        shop: {
          domain: shopDomain
        },
        // Add current page context
        page: {
          url: window.location.href,
          path: window.location.pathname,
          title: document.title
        }
      };

      // Initialize chatbot if available
      if (window.Chatbot && typeof window.Chatbot.init === "function") {
        window.Chatbot.init(chatbotConfig);
        console.log("Chatbot initialized successfully", chatbotConfig);
      } else {
        console.warn("Chatbot object not found or init method not available");
      }
    } catch (error) {
      console.error("Error initializing chatbot:", error);
    }
  }

  // Start loading the chatbot script
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", loadChatbotScript);
  } else {
    loadChatbotScript();
  }
})();