#!/usr/bin/env node

// Custom development server that bypasses Prisma issues
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 Starting Shopify App Development Server...');
console.log('⚠️  Bypassing Prisma generation due to permission issues');

// Set environment variables
process.env.SHOPIFY_API_KEY = 'fc5ccb3037e1322db6915a4de00972c1';
process.env.SHOPIFY_APP_URL = 'https://distributions-italian-directors-then.trycloudflare.com';

// Start Vite development server
const viteProcess = spawn('npx', ['vite', 'dev', '--port', '52256', '--host', '0.0.0.0'], {
  cwd: __dirname,
  stdio: 'inherit',
  shell: true
});

viteProcess.on('error', (error) => {
  console.error('❌ Failed to start development server:', error);
  process.exit(1);
});

viteProcess.on('close', (code) => {
  console.log(`Development server exited with code ${code}`);
  process.exit(code);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development server...');
  viteProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down development server...');
  viteProcess.kill('SIGTERM');
});
