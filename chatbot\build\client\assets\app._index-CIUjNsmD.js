import{R as n,r as g,j as e}from"./index-C0U6NBub.js";import{u as N,t as O}from"./components-qeoBKuxV.js";import{W as R,c as Z,u as F,d as S,e as P,f as L,a as v,I as x,B as l,T as i,g as D,P as W,C,h as H,S as z}from"./Page-BAvRnlvb.js";import{B as K,T as X,L as k,a as y}from"./TitleBar-DiPv-53d.js";import{u as Y}from"./context-BJzNQyky.js";var M=function(a){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},a),n.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),n.createElement("path",{d:"M11 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),n.createElement("path",{fillRule:"evenodd",d:"M11.237 3.177a1.75 1.75 0 0 0-2.474 0l-5.586 5.585a1.75 1.75 0 0 0 0 2.475l5.586 5.586a1.75 1.75 0 0 0 2.474 0l5.586-5.586a1.75 1.75 0 0 0 0-2.475l-5.586-5.585Zm-1.414 1.06a.25.25 0 0 1 .354 0l5.586 5.586a.25.25 0 0 1 0 .354l-5.586 5.585a.25.25 0 0 1-.354 0l-5.586-5.585a.25.25 0 0 1 0-.354l5.586-5.586Z"}))};M.displayName="AlertDiamondIcon";var T=function(a){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},a),n.createElement("path",{d:"M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),n.createElement("path",{d:"M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),n.createElement("path",{fillRule:"evenodd",d:"M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"}))};T.displayName="AlertTriangleIcon";var _=function(a){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},a),n.createElement("path",{fillRule:"evenodd",d:"M15.78 5.97a.75.75 0 0 1 0 1.06l-6.5 6.5a.75.75 0 0 1-1.06 0l-3.25-3.25a.75.75 0 1 1 1.06-1.06l2.72 2.72 5.97-5.97a.75.75 0 0 1 1.06 0Z"}))};_.displayName="CheckIcon";var U=function(a){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},a),n.createElement("path",{d:"M10 14a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 1 1.5 0v3.5a.75.75 0 0 1-.75.75Z"}),n.createElement("path",{d:"M9 7a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z"}),n.createElement("path",{fillRule:"evenodd",d:"M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"}))};U.displayName="InfoIcon";var A=function(a){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},a),n.createElement("path",{d:"M12.72 13.78a.75.75 0 1 0 1.06-1.06l-2.72-2.72 2.72-2.72a.75.75 0 0 0-1.06-1.06l-2.72 2.72-2.72-2.72a.75.75 0 0 0-1.06 1.06l2.72 2.72-2.72 2.72a.75.75 0 1 0 1.06 1.06l2.72-2.72 2.72 2.72Z"}))};A.displayName="XIcon";var E={Banner:"Polaris-Banner",keyFocused:"Polaris-Banner--keyFocused",withinContentContainer:"Polaris-Banner--withinContentContainer",withinPage:"Polaris-Banner--withinPage",DismissIcon:"Polaris-Banner__DismissIcon","text-success-on-bg-fill":"Polaris-Banner--textSuccessOnBgFill","text-success":"Polaris-Banner__text--success","text-warning-on-bg-fill":"Polaris-Banner--textWarningOnBgFill","text-warning":"Polaris-Banner__text--warning","text-critical-on-bg-fill":"Polaris-Banner--textCriticalOnBgFill","text-critical":"Polaris-Banner__text--critical","text-info-on-bg-fill":"Polaris-Banner--textInfoOnBgFill","text-info":"Polaris-Banner__text--info","icon-secondary":"Polaris-Banner__icon--secondary"};const I={success:{withinPage:{background:"bg-fill-success",text:"text-success-on-bg-fill",icon:"text-success-on-bg-fill"},withinContentContainer:{background:"bg-surface-success",text:"text-success",icon:"text-success"},icon:_},warning:{withinPage:{background:"bg-fill-warning",text:"text-warning-on-bg-fill",icon:"text-warning-on-bg-fill"},withinContentContainer:{background:"bg-surface-warning",text:"text-warning",icon:"text-warning"},icon:T},critical:{withinPage:{background:"bg-fill-critical",text:"text-critical-on-bg-fill",icon:"text-critical-on-bg-fill"},withinContentContainer:{background:"bg-surface-critical",text:"text-critical",icon:"text-critical"},icon:M},info:{withinPage:{background:"bg-fill-info",text:"text-info-on-bg-fill",icon:"text-info-on-bg-fill"},withinContentContainer:{background:"bg-surface-info",text:"text-info",icon:"text-info"},icon:U}};function $(t){const a=g.useRef(null),[u,s]=g.useState(!1);return g.useImperativeHandle(t,()=>({focus:()=>{var r;(r=a.current)==null||r.focus(),s(!0)}}),[]),{wrapperRef:a,handleKeyUp:r=>{r.target===a.current&&s(!0)},handleBlur:()=>s(!1),handleMouseUp:r=>{r.currentTarget.blur(),s(!1)},shouldShowFocus:u}}const G=g.forwardRef(function(a,u){const{tone:s,stopAnnouncements:c}=a,o=g.useContext(R),{wrapperRef:d,handleKeyUp:r,handleBlur:h,handleMouseUp:p,shouldShowFocus:f}=$(u),w=Z(E.Banner,f&&E.keyFocused,o?E.withinContentContainer:E.withinPage);return n.createElement(K.Provider,{value:!0},n.createElement("div",{className:w,tabIndex:0,ref:d,role:s==="warning"||s==="critical"?"alert":"status","aria-live":c?"off":"polite",onMouseUp:p,onKeyUp:r,onBlur:h},n.createElement(V,a)))});function V({tone:t="info",icon:a,hideIcon:u,onDismiss:s,action:c,secondaryAction:o,title:d,children:r}){const h=F(),p=g.useContext(R),f=!d&&!p,w=Object.keys(I).includes(t)?t:"info",j=I[w][p?"withinContentContainer":"withinPage"],m={backgroundColor:j.background,textColor:j.text,bannerTitle:d?n.createElement(i,{as:"h2",variant:"headingSm",breakWord:!0},d):null,bannerIcon:u?null:n.createElement("span",{className:E[j.icon]},n.createElement(P,{source:a??I[w].icon})),actionButtons:c||o?n.createElement(L,null,c&&n.createElement(S,Object.assign({onClick:c.onAction},c),c.content),o&&n.createElement(S,Object.assign({onClick:o.onAction},o),o.content)):null,dismissButton:s?n.createElement(S,{variant:"tertiary",icon:n.createElement("span",{className:E[f?"icon-secondary":j.icon]},n.createElement(P,{source:A})),onClick:s,accessibilityLabel:h.translate("Polaris.Banner.dismissButton")}):null},b=r?n.createElement(i,{as:"span",variant:"bodyMd"},r):null;return p?n.createElement(Q,m,b):f?n.createElement(J,m,b):n.createElement(q,m,b)}function q({backgroundColor:t,textColor:a,bannerTitle:u,bannerIcon:s,actionButtons:c,dismissButton:o,children:d}){const{smUp:r}=Y(),h=d||c;return n.createElement(v,{width:"100%"},n.createElement(l,{align:"space-between"},n.createElement(v,{background:t,color:a,borderStartStartRadius:r?"300":void 0,borderStartEndRadius:r?"300":void 0,borderEndStartRadius:!h&&r?"300":void 0,borderEndEndRadius:!h&&r?"300":void 0,padding:"300"},n.createElement(x,{align:"space-between",blockAlign:"center",gap:"200",wrap:!1},n.createElement(x,{gap:"100",wrap:!1},s,u),o)),h&&n.createElement(v,{padding:{xs:"300",md:"400"},paddingBlockStart:"300"},n.createElement(l,{gap:"200"},n.createElement("div",null,d),c))))}function J({backgroundColor:t,bannerIcon:a,actionButtons:u,dismissButton:s,children:c}){const[o,d]=g.useState("center"),r=g.useRef(null),h=g.useRef(null),p=g.useRef(null),f=g.useCallback(()=>{var m,b,B;const w=(m=r.current)==null?void 0:m.offsetHeight,j=((b=h.current)==null?void 0:b.offsetHeight)||((B=p.current)==null?void 0:B.offsetHeight);!w||!j||(w>j?d("start"):d("center"))},[]);return g.useEffect(()=>f(),[f]),D("resize",f),n.createElement(v,{width:"100%",padding:"300",borderRadius:"300"},n.createElement(x,{align:"space-between",blockAlign:o,wrap:!1},n.createElement(v,{width:"100%"},n.createElement(x,{gap:"200",wrap:!1,blockAlign:o},a?n.createElement("div",{ref:h},n.createElement(v,{background:t,borderRadius:"200",padding:"100"},a)):null,n.createElement(v,{ref:r,width:"100%"},n.createElement(l,{gap:"200"},n.createElement("div",null,c),u)))),n.createElement("div",{ref:p,className:E.DismissIcon},s)))}function Q({backgroundColor:t,textColor:a,bannerTitle:u,bannerIcon:s,actionButtons:c,dismissButton:o,children:d}){return n.createElement(v,{width:"100%",background:t,padding:"200",borderRadius:"200",color:a},n.createElement(x,{align:"space-between",blockAlign:"start",wrap:!1,gap:"200"},n.createElement(x,{gap:"150",wrap:!1},s,n.createElement(v,{width:"100%"},n.createElement(l,{gap:"200"},n.createElement(l,{gap:"050"},u,n.createElement("div",null,d)),c))),o))}const ee=new Proxy({},{get(t,a){throw Error(`shopify.${String(a)} can't be used in a server environment. You likely need to move this code into an Effect.`)}});function ne(){if(typeof window>"u")return ee;if(!window.shopify)throw Error("The shopify global is not defined. This likely means the App Bridge script tag was not added correctly to this page");return window.shopify}function oe(){var h,p,f,w,j;const t=N(),a=O(),u=ne(),[s,c]=g.useState(!1),o=["loading","submitting"].includes(a.state);g.useEffect(()=>{var m,b;(m=a.data)!=null&&m.success?(u.toast.show(a.data.message),c(!0),setTimeout(()=>{window.location.reload()},1e3)):(b=a.data)!=null&&b.error&&u.toast.show(a.data.error,{isError:!0})},[a.data,u]);const d=()=>{a.submit({action:"install_script"},{method:"POST"})},r=()=>{a.submit({action:"uninstall_script"},{method:"POST"})};return e.jsxs(W,{children:[e.jsx(X,{title:"Chatbot Dashboard"}),t.error&&e.jsx(G,{status:"critical",title:"Error loading dashboard",children:e.jsx("p",{children:t.error})}),e.jsx(l,{gap:"500",children:e.jsxs(k,{children:[e.jsx(k.Section,{children:e.jsx(C,{children:e.jsxs(l,{gap:"500",children:[e.jsxs(l,{gap:"200",children:[e.jsxs(i,{as:"h2",variant:"headingMd",children:["🤖 Chatbot Status for ",t.shop]}),e.jsxs(x,{gap:"200",align:"start",children:[e.jsx(H,{status:t.isInstalled?"success":"attention",size:"medium",children:t.isInstalled?"Active":"Not Installed"}),s&&e.jsx(z,{size:"small"})]})]}),t.isInstalled?e.jsxs(l,{gap:"300",children:[e.jsx(i,{variant:"bodyMd",as:"p",color:"success",children:"✅ Your chatbot is successfully installed and running on your storefront!"}),e.jsx(v,{padding:"400",background:"bg-surface-success",borderWidth:"025",borderRadius:"200",borderColor:"border-success",children:e.jsxs(l,{gap:"200",children:[e.jsx(i,{as:"h4",variant:"headingSm",children:"Script Tag Details:"}),e.jsxs(i,{variant:"bodySm",children:[e.jsx("strong",{children:"Script URL:"})," ",(h=t.chatbotScriptTag)==null?void 0:h.src]}),e.jsxs(i,{variant:"bodySm",children:[e.jsx("strong",{children:"Display Scope:"})," ",((p=t.chatbotScriptTag)==null?void 0:p.display_scope)||"online_store"]}),e.jsxs(i,{variant:"bodySm",children:[e.jsx("strong",{children:"Created:"})," ",(f=t.chatbotScriptTag)!=null&&f.created_at?new Date(t.chatbotScriptTag.created_at).toLocaleString():"Unknown"]})]})}),e.jsxs(x,{gap:"300",children:[e.jsx(S,{variant:"primary",tone:"critical",loading:o,onClick:r,children:"Uninstall Chatbot"}),e.jsx(S,{url:`https://${t.shop}`,target:"_blank",variant:"plain",children:"View Storefront"})]})]}):e.jsxs(l,{gap:"300",children:[e.jsx(i,{variant:"bodyMd",as:"p",children:"Your chatbot is not currently installed on your storefront. Click the button below to install it."}),e.jsx(S,{variant:"primary",loading:o,onClick:d,children:"Install Chatbot"})]})]})})}),e.jsx(k.Section,{variant:"oneThird",children:e.jsxs(l,{gap:"500",children:[e.jsx(C,{children:e.jsxs(l,{gap:"200",children:[e.jsx(i,{as:"h2",variant:"headingMd",children:"📊 Script Tags Overview"}),e.jsxs(i,{variant:"bodyMd",children:["Total script tags: ",e.jsx("strong",{children:((w=t.scriptTags)==null?void 0:w.length)||0})]}),((j=t.scriptTags)==null?void 0:j.length)>0&&e.jsx(v,{padding:"300",background:"bg-surface-secondary",borderWidth:"025",borderRadius:"200",borderColor:"border",children:e.jsxs(l,{gap:"100",children:[t.scriptTags.slice(0,5).map((m,b)=>{var B;return e.jsxs(i,{variant:"bodySm",children:["• ",((B=m.src)==null?void 0:B.split("/").pop())||"Unknown script"]},b)}),t.scriptTags.length>5&&e.jsxs(i,{variant:"bodySm",color:"subdued",children:["... and ",t.scriptTags.length-5," more"]})]})})]})}),e.jsx(C,{children:e.jsxs(l,{gap:"200",children:[e.jsx(i,{as:"h2",variant:"headingMd",children:"🔧 Configuration"}),e.jsxs(l,{gap:"200",children:[e.jsxs(x,{align:"space-between",children:[e.jsx(i,{as:"span",variant:"bodyMd",children:"App URL"}),e.jsx(i,{variant:"bodySm",color:"subdued",children:t.appUrl?new URL(t.appUrl).hostname:"Not configured"})]}),e.jsxs(x,{align:"space-between",children:[e.jsx(i,{as:"span",variant:"bodyMd",children:"Script URL"}),e.jsx(i,{variant:"bodySm",color:"subdued",children:t.chatbotScriptUrl?"/chatbot-loader.js":"Not available"})]}),e.jsxs(x,{align:"space-between",children:[e.jsx(i,{as:"span",variant:"bodyMd",children:"Embed Script"}),e.jsx(i,{variant:"bodySm",color:"subdued",children:"/chatbot-embed.js"})]})]})]})}),e.jsx(C,{children:e.jsxs(l,{gap:"200",children:[e.jsx(i,{as:"h2",variant:"headingMd",children:"📚 Features"}),e.jsxs(y,{children:[e.jsx(y.Item,{children:"Automatic customer identification"}),e.jsx(y.Item,{children:"Real-time chat interface"}),e.jsx(y.Item,{children:"Mobile-responsive design"}),e.jsx(y.Item,{children:"Easy installation & removal"})]})]})})]})})]})})]})}export{oe as default};
