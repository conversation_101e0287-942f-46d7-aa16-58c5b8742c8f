// Temporary workaround for Prisma permission issue
let prisma;

try {
  const { PrismaClient } = await import("@prisma/client");

  if (process.env.NODE_ENV !== "production") {
    if (!global.prismaGlobal) {
      global.prismaGlobal = new PrismaClient();
    }
  }

  prisma = global.prismaGlobal ?? new PrismaClient();
} catch (error) {
  console.warn("Prisma client not available, using mock:", error.message);
  // Mock Prisma client for development
  prisma = {
    session: {
      findFirst: () => Promise.resolve(null),
      create: () => Promise.resolve({}),
      update: () => Promise.resolve({}),
      delete: () => Promise.resolve({}),
    }
  };
}

export default prisma;
