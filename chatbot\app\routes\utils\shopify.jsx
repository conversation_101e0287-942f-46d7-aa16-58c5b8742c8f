const Shopify = require("@shopify/shopify-api").default;

Shopify.Context.initialize({
  API_KEY: process.env.SHOPIFY_API_KEY,
  API_SECRET_KEY: process.env.SHOPIFY_API_SECRET,
  SCOPES: process.env.SCOPES.split(","),
  HOST_NAME: new URL(process.env.SHOPIFY_APP_URL).host,
  IS_EMBEDDED_APP: false,
  API_VERSION: "2024-01",
  SESSION_STORAGE: new Shopify.Session.MemorySessionStorage(),
});

const registerScript = async (shop, accessToken) => {
  const response = await fetch(`https://${shop}/admin/api/2024-01/script_tags.json`, {
    method: "POST",
    headers: {
      "X-Shopify-Access-Token": accessToken,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      script_tag: {
        event: "onload",
        src: `/chatbot-loader.js`,
      },
    }),
  });

  return await response.json();
};

module.exports = { Shopify, registerScript };