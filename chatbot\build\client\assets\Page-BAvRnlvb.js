import{R as n,r as u,d as Mr}from"./index-C0U6NBub.js";import{j as Sn,e as Cn,T as Lr,f as Fr,g as Or,c as Rr,k as Ge,u as Bt,a as kt,L as Wr,l as tn,I as $r,b as Hr,P as In,m as Dr,R as _t,d as zr,h as jr,s as Tn,S as Ur,o as We,p as Gr,E as ct,q as An,r as Zr,v as Vr,w as qr,x as Xr,M as Yr}from"./context-BJzNQyky.js";let de;(function(e){e[e.Backspace=8]="Backspace",e[e.Tab=9]="Tab",e[e.Enter=13]="Enter",e[e.Shift=16]="Shift",e[e.Ctrl=17]="Ctrl",e[e.Alt=18]="Alt",e[e.Pause=19]="Pause",e[e.CapsLock=20]="CapsLock",e[e.Escape=27]="Escape",e[e.Space=32]="Space",e[e.PageUp=33]="PageUp",e[e.PageDown=34]="PageDown",e[e.End=35]="End",e[e.Home=36]="Home",e[e.LeftArrow=37]="LeftArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.DownArrow=40]="DownArrow",e[e.Insert=45]="Insert",e[e.Delete=46]="Delete",e[e.Key0=48]="Key0",e[e.Key1=49]="Key1",e[e.Key2=50]="Key2",e[e.Key3=51]="Key3",e[e.Key4=52]="Key4",e[e.Key5=53]="Key5",e[e.Key6=54]="Key6",e[e.Key7=55]="Key7",e[e.Key8=56]="Key8",e[e.Key9=57]="Key9",e[e.KeyA=65]="KeyA",e[e.KeyB=66]="KeyB",e[e.KeyC=67]="KeyC",e[e.KeyD=68]="KeyD",e[e.KeyE=69]="KeyE",e[e.KeyF=70]="KeyF",e[e.KeyG=71]="KeyG",e[e.KeyH=72]="KeyH",e[e.KeyI=73]="KeyI",e[e.KeyJ=74]="KeyJ",e[e.KeyK=75]="KeyK",e[e.KeyL=76]="KeyL",e[e.KeyM=77]="KeyM",e[e.KeyN=78]="KeyN",e[e.KeyO=79]="KeyO",e[e.KeyP=80]="KeyP",e[e.KeyQ=81]="KeyQ",e[e.KeyR=82]="KeyR",e[e.KeyS=83]="KeyS",e[e.KeyT=84]="KeyT",e[e.KeyU=85]="KeyU",e[e.KeyV=86]="KeyV",e[e.KeyW=87]="KeyW",e[e.KeyX=88]="KeyX",e[e.KeyY=89]="KeyY",e[e.KeyZ=90]="KeyZ",e[e.LeftMeta=91]="LeftMeta",e[e.RightMeta=92]="RightMeta",e[e.Select=93]="Select",e[e.Numpad0=96]="Numpad0",e[e.Numpad1=97]="Numpad1",e[e.Numpad2=98]="Numpad2",e[e.Numpad3=99]="Numpad3",e[e.Numpad4=100]="Numpad4",e[e.Numpad5=101]="Numpad5",e[e.Numpad6=102]="Numpad6",e[e.Numpad7=103]="Numpad7",e[e.Numpad8=104]="Numpad8",e[e.Numpad9=105]="Numpad9",e[e.Multiply=106]="Multiply",e[e.Add=107]="Add",e[e.Subtract=109]="Subtract",e[e.Decimal=110]="Decimal",e[e.Divide=111]="Divide",e[e.F1=112]="F1",e[e.F2=113]="F2",e[e.F3=114]="F3",e[e.F4=115]="F4",e[e.F5=116]="F5",e[e.F6=117]="F6",e[e.F7=118]="F7",e[e.F8=119]="F8",e[e.F9=120]="F9",e[e.F10=121]="F10",e[e.F11=122]="F11",e[e.F12=123]="F12",e[e.NumLock=144]="NumLock",e[e.ScrollLock=145]="ScrollLock",e[e.Semicolon=186]="Semicolon",e[e.Equals=187]="Equals",e[e.Comma=188]="Comma",e[e.Dash=189]="Dash",e[e.Period=190]="Period",e[e.ForwardSlash=191]="ForwardSlash",e[e.GraveAccent=192]="GraveAccent",e[e.OpenBracket=219]="OpenBracket",e[e.BackSlash=220]="BackSlash",e[e.CloseBracket=221]="CloseBracket",e[e.SingleQuote=222]="SingleQuote"})(de||(de={}));function wn(e){const t=typeof e;return e!=null&&(t==="object"||t==="function")}function w(...e){return e.filter(Boolean).join(" ")}function X(e,t){return`${e}${t.charAt(0).toUpperCase()}${t.slice(1)}`}function Mt(e){const t=Object.entries(e).filter(([r,o])=>o!=null);return t.length?Object.fromEntries(t):void 0}function be(e,t,r,o){if(!o)return{};let a;return wn(o)?a=Object.fromEntries(Object.entries(o).map(([i,s])=>[i,`var(--p-${r}-${s})`])):a={[Sn[0]]:`var(--p-${r}-${o})`},Object.fromEntries(Object.entries(a).map(([i,s])=>[`--pc-${e}-${t}-${i}`,s]))}function it(e,t,r){return r?wn(r)?Object.fromEntries(Object.entries(r).map(([o,a])=>[`--pc-${e}-${t}-${o}`,a])):{[`--pc-${e}-${t}-${Sn[0]}`]:r}:{}}var Qr={themeContainer:"Polaris-ThemeProvider--themeContainer"};const Jr=["light","dark-experimental"],Kr=e=>Jr.includes(e);function eo(e){const{as:t="div",children:r,className:o,theme:a=Cn}=e;return n.createElement(Lr.Provider,{value:a},n.createElement(Fr.Provider,{value:Or(a)},n.createElement(t,{"data-portal-id":e["data-portal-id"],className:w(Rr(a),Qr.themeContainer,o)},r)))}const to=u.createContext(!1);function yn(e,t,r,o){const a=u.useRef(t),i=u.useRef(o);Ge(()=>{a.current=t},[t]),Ge(()=>{i.current=o},[o]),u.useEffect(()=>{if(!(typeof e=="string"&&r!==null))return;let s;if(typeof r>"u")s=window;else if("current"in r){if(r.current===null)return;s=r.current}else s=r;const l=i.current,c=d=>a.current(d);return s.addEventListener(e,c,l),()=>{s.removeEventListener(e,c,l)}},[e,r])}var Nn=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),n.createElement("path",{d:"M11 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),n.createElement("path",{fillRule:"evenodd",d:"M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"}))};Nn.displayName="AlertCircleIcon";var Bn=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{fillRule:"evenodd",d:"M16.5 10a.75.75 0 0 1-.75.75h-9.69l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 1 1 1.06 1.06l-2.72 2.72h9.69a.75.75 0 0 1 .75.75Z"}))};Bn.displayName="ArrowLeftIcon";var Lt=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{fillRule:"evenodd",d:"M5.72 8.47a.75.75 0 0 1 1.06 0l3.47 3.47 3.47-3.47a.75.75 0 1 1 1.06 1.06l-4 4a.75.75 0 0 1-1.06 0l-4-4a.75.75 0 0 1 0-1.06Z"}))};Lt.displayName="ChevronDownIcon";var kn=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{fillRule:"evenodd",d:"M11.764 5.204a.75.75 0 0 1 .032 1.06l-3.516 3.736 3.516 3.736a.75.75 0 1 1-1.092 1.028l-4-4.25a.75.75 0 0 1 0-1.028l4-4.25a.75.75 0 0 1 1.06-.032Z"}))};kn.displayName="ChevronLeftIcon";var _n=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{fillRule:"evenodd",d:"M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"}))};_n.displayName="ChevronRightIcon";var Ft=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{fillRule:"evenodd",d:"M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"}))};Ft.displayName="ChevronUpIcon";var Mn=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{d:"M6 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}),n.createElement("path",{d:"M11.5 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}),n.createElement("path",{d:"M17 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))};Mn.displayName="MenuHorizontalIcon";var Ln=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{fillRule:"evenodd",d:"M12.323 13.383a5.5 5.5 0 1 1 1.06-1.06l2.897 2.897a.75.75 0 1 1-1.06 1.06l-2.897-2.897Zm.677-4.383a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"}))};Ln.displayName="SearchIcon";var Fn=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{d:"M10.884 4.323a1.25 1.25 0 0 0-1.768 0l-2.646 2.647a.75.75 0 0 0 1.06 1.06l2.47-2.47 2.47 2.47a.75.75 0 1 0 1.06-1.06l-2.646-2.647Z"}),n.createElement("path",{d:"m13.53 13.03-2.646 2.647a1.25 1.25 0 0 1-1.768 0l-2.646-2.647a.75.75 0 0 1 1.06-1.06l2.47 2.47 2.47-2.47a.75.75 0 0 1 1.06 1.06Z"}))};Fn.displayName="SelectIcon";var On=function(t){return n.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),n.createElement("path",{d:"M13.03 6.97a.75.75 0 0 1 0 1.06l-1.97 1.97 1.97 1.97a.75.75 0 1 1-1.06 1.06l-1.97-1.97-1.97 1.97a.75.75 0 0 1-1.06-1.06l1.97-1.97-1.97-1.97a.75.75 0 0 1 1.06-1.06l1.97 1.97 1.97-1.97a.75.75 0 0 1 1.06 0Z"}),n.createElement("path",{fillRule:"evenodd",d:"M10 17a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm0-1.5a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11Z"}))};On.displayName="XCircleIcon";function no(e){const{top:t,left:r,bottom:o,right:a}=e.getBoundingClientRect();return t>=0&&a<=window.innerWidth&&o<=window.innerHeight&&r>=0}const yt='a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not([aria-disabled="true"]):not([tabindex="-1"]):not(:disabled),*[tabindex]',nn='a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not([aria-disabled="true"]):not([tabindex="-1"]):not(:disabled),*[tabindex]:not([tabindex="-1"])',ro='a[role="menuitem"],frame[role="menuitem"],iframe[role="menuitem"],input[role="menuitem"]:not([type=hidden]):not(:disabled),select[role="menuitem"]:not(:disabled),textarea[role="menuitem"]:not(:disabled),button[role="menuitem"]:not(:disabled),*[tabindex]:not([tabindex="-1"])',ut=({currentTarget:e})=>e.blur();function oo(e,t){const r=[...document.querySelectorAll(yt)],o=r.indexOf(e)+1,a=r.slice(o);for(const i of a)if(no(i)&&(!t||t&&t(i)))return i;return null}function ao(e,t=!0){return!t&&Ot(e,yt)?e:e.querySelector(yt)}function $e(e){const t="a,button,frame,iframe,input:not([type=hidden]),select,textarea,*[tabindex]";return Ot(e,t)?e:e.querySelector(t)}function rn(e,t){const r=oo(e,t);return r&&r instanceof HTMLElement?(r.focus(),!0):!1}function io(e,t=!0){return!t&&Ot(e,nn)?e:e.querySelector(nn)}function so(e,t){const r=Rn(e),o=Wn(r,t);o===-1?r[0].focus():r[(o-1+r.length)%r.length].focus()}function lo(e,t){const r=Rn(e),o=Wn(r,t);o===-1?r[0].focus():r[(o+1)%r.length].focus()}function Rn(e){return e.querySelectorAll(ro)}function Wn(e,t){let r=0;for(const o of e){if(o===t)break;r++}return r===e.length?-1:r}function Ot(e,t){if(e.matches)return e.matches(t);const r=(e.ownerDocument||document).querySelectorAll(t);let o=r.length;for(;--o>=0&&r.item(o)!==e;)return o>-1}var G={Button:"Polaris-Button",disabled:"Polaris-Button--disabled",pressed:"Polaris-Button--pressed",variantPrimary:"Polaris-Button--variantPrimary",variantSecondary:"Polaris-Button--variantSecondary",variantTertiary:"Polaris-Button--variantTertiary",variantPlain:"Polaris-Button--variantPlain",removeUnderline:"Polaris-Button--removeUnderline",variantMonochromePlain:"Polaris-Button--variantMonochromePlain",toneSuccess:"Polaris-Button--toneSuccess",toneCritical:"Polaris-Button--toneCritical",sizeMicro:"Polaris-Button--sizeMicro",sizeSlim:"Polaris-Button--sizeSlim",sizeMedium:"Polaris-Button--sizeMedium",sizeLarge:"Polaris-Button--sizeLarge",textAlignCenter:"Polaris-Button--textAlignCenter",textAlignStart:"Polaris-Button--textAlignStart",textAlignLeft:"Polaris-Button--textAlignLeft",textAlignEnd:"Polaris-Button--textAlignEnd",textAlignRight:"Polaris-Button--textAlignRight",fullWidth:"Polaris-Button--fullWidth",iconOnly:"Polaris-Button--iconOnly",iconWithText:"Polaris-Button--iconWithText",disclosure:"Polaris-Button--disclosure",loading:"Polaris-Button--loading",pressable:"Polaris-Button--pressable",hidden:"Polaris-Button--hidden",Icon:"Polaris-Button__Icon",Spinner:"Polaris-Button__Spinner"},He={Icon:"Polaris-Icon",toneInherit:"Polaris-Icon--toneInherit",toneBase:"Polaris-Icon--toneBase",toneSubdued:"Polaris-Icon--toneSubdued",toneCaution:"Polaris-Icon--toneCaution",toneWarning:"Polaris-Icon--toneWarning",toneCritical:"Polaris-Icon--toneCritical",toneInteractive:"Polaris-Icon--toneInteractive",toneInfo:"Polaris-Icon--toneInfo",toneSuccess:"Polaris-Icon--toneSuccess",tonePrimary:"Polaris-Icon--tonePrimary",toneEmphasis:"Polaris-Icon--toneEmphasis",toneMagic:"Polaris-Icon--toneMagic",toneTextCaution:"Polaris-Icon--toneTextCaution",toneTextWarning:"Polaris-Icon--toneTextWarning",toneTextCritical:"Polaris-Icon--toneTextCritical",toneTextInfo:"Polaris-Icon--toneTextInfo",toneTextPrimary:"Polaris-Icon--toneTextPrimary",toneTextSuccess:"Polaris-Icon--toneTextSuccess",toneTextMagic:"Polaris-Icon--toneTextMagic",Svg:"Polaris-Icon__Svg",Img:"Polaris-Icon__Img",Placeholder:"Polaris-Icon__Placeholder"},pe={root:"Polaris-Text--root",block:"Polaris-Text--block",truncate:"Polaris-Text--truncate",visuallyHidden:"Polaris-Text--visuallyHidden",start:"Polaris-Text--start",center:"Polaris-Text--center",end:"Polaris-Text--end",justify:"Polaris-Text--justify",base:"Polaris-Text--base",inherit:"Polaris-Text--inherit",disabled:"Polaris-Text--disabled",success:"Polaris-Text--success",critical:"Polaris-Text--critical",caution:"Polaris-Text--caution",subdued:"Polaris-Text--subdued",magic:"Polaris-Text--magic","magic-subdued":"Polaris-Text__magic--subdued","text-inverse":"Polaris-Text__text--inverse","text-inverse-secondary":"Polaris-Text--textInverseSecondary",headingXs:"Polaris-Text--headingXs",headingSm:"Polaris-Text--headingSm",headingMd:"Polaris-Text--headingMd",headingLg:"Polaris-Text--headingLg",headingXl:"Polaris-Text--headingXl",heading2xl:"Polaris-Text--heading2xl",heading3xl:"Polaris-Text--heading3xl",bodyXs:"Polaris-Text--bodyXs",bodySm:"Polaris-Text--bodySm",bodyMd:"Polaris-Text--bodyMd",bodyLg:"Polaris-Text--bodyLg",regular:"Polaris-Text--regular",medium:"Polaris-Text--medium",semibold:"Polaris-Text--semibold",bold:"Polaris-Text--bold",break:"Polaris-Text--break",numeric:"Polaris-Text--numeric","line-through":"Polaris-Text__line--through"};const W=({alignment:e,as:t,breakWord:r,children:o,tone:a,fontWeight:i,id:s,numeric:l=!1,truncate:c=!1,variant:d,visuallyHidden:p=!1,textDecorationLine:h})=>{const f=t||(p?"span":"p"),m=w(pe.root,d&&pe[d],i&&pe[i],(e||c)&&pe.block,e&&pe[e],r&&pe.break,a&&pe[a],l&&pe.numeric,c&&pe.truncate,p&&pe.visuallyHidden,h&&pe[h]);return n.createElement(f,Object.assign({className:m},s&&{id:s}),o)};function me({source:e,tone:t,accessibilityLabel:r}){let o;typeof e=="function"?o="function":e==="placeholder"?o="placeholder":o="external";const a=w(He.Icon,t&&He[X("tone",t)]),{mdDown:i}=Bt(),s=e,l={function:n.createElement(s,Object.assign({className:He.Svg,focusable:"false","aria-hidden":"true"},i?{viewBox:"1 1 18 18"}:{})),placeholder:n.createElement("div",{className:He.Placeholder}),external:n.createElement("img",{className:He.Img,src:`data:image/svg+xml;utf8,${e}`,alt:"","aria-hidden":"true"})};return n.createElement("span",{className:a},r&&n.createElement(W,{as:"span",visuallyHidden:!0},r),l[o])}var on={Spinner:"Polaris-Spinner",sizeSmall:"Polaris-Spinner--sizeSmall",sizeLarge:"Polaris-Spinner--sizeLarge"};function $n({size:e="large",accessibilityLabel:t,hasFocusableParent:r}){const o=kt(),a=w(on.Spinner,e&&on[X("size",e)]),i=e==="large"?n.createElement("svg",{viewBox:"0 0 44 44",xmlns:"http://www.w3.org/2000/svg"},n.createElement("path",{d:"M15.542 1.487A21.507 21.507 0 00.5 22c0 11.874 9.626 21.5 21.5 21.5 9.847 0 18.364-6.675 20.809-16.072a1.5 1.5 0 00-2.904-.756C37.803 34.755 30.473 40.5 22 40.5 11.783 40.5 3.5 32.217 3.5 22c0-8.137 5.3-15.247 12.942-17.65a1.5 1.5 0 10-.9-2.863z"})):n.createElement("svg",{viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},n.createElement("path",{d:"M7.229 1.173a9.25 9.25 0 1011.655 11.412 1.25 1.25 0 10-2.4-.698 6.75 6.75 0 11-8.506-8.329 1.25 1.25 0 10-.75-2.385z"})),s={...!r&&{role:"status"}},l=(o||!r)&&n.createElement(W,{as:"span",visuallyHidden:!0},t);return n.createElement(n.Fragment,null,n.createElement("span",{className:a},i),n.createElement("span",s,l))}function co(e,t){const r=u.useCallback(o=>{e&&(o.preventDefault(),o.stopPropagation())},[e]);return e?r:t}function uo(){return u.useContext(Wr)}const Hn=u.memo(u.forwardRef(function(t,r){const o=uo();if(o)return n.createElement(o,Object.assign({},tn.props,t,{ref:r}));const{external:a,url:i,target:s,...l}=t;let c;a?c="_blank":c=s??void 0;const d=c==="_blank"?"noopener noreferrer":void 0;return n.createElement("a",Object.assign({target:c},l,{href:i,rel:d},tn.props,{ref:r}))}));function po({id:e,children:t,className:r,url:o,external:a,target:i,download:s,submit:l,disabled:c,loading:d,pressed:p,accessibilityLabel:h,role:f,ariaControls:m,ariaExpanded:b,ariaDescribedBy:P,ariaChecked:E,onClick:I,onFocus:S,onBlur:y,onKeyDown:A,onKeyPress:N,onKeyUp:C,onMouseEnter:g,onTouchStart:v,...T}){let M;const H={id:e,className:r,"aria-label":h},F={...H,role:f,onClick:I,onFocus:S,onBlur:y,onMouseUp:ut,onMouseEnter:g,onTouchStart:v},R=co(c,I);return o?M=c?n.createElement("a",H,t):n.createElement(Hn,Object.assign({},F,{url:o,external:a,target:i,download:s},T),t):M=n.createElement("button",Object.assign({},F,{"aria-disabled":c,type:l?"submit":"button","aria-busy":d?!0:void 0,"aria-controls":m,"aria-expanded":b,"aria-describedby":P,"aria-checked":E,"aria-pressed":p,onKeyDown:A,onKeyUp:C,onKeyPress:N,onClick:R,tabIndex:c?-1:void 0},T),t),M}class mo extends Error{constructor(t=""){super(`${t&&`${t} `}Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.`),this.name="MissingAppProviderError"}}function fe(){const e=u.useContext($r);if(!e)throw new mo("No i18n was provided.");return e}function Ne({id:e,children:t,url:r,disabled:o,external:a,download:i,target:s,submit:l,loading:c,pressed:d,accessibilityLabel:p,role:h,ariaControls:f,ariaExpanded:m,ariaDescribedBy:b,ariaChecked:P,onClick:E,onFocus:I,onBlur:S,onKeyDown:y,onKeyPress:A,onKeyUp:N,onMouseEnter:C,onTouchStart:g,onPointerDown:v,icon:T,disclosure:M,removeUnderline:H,size:F="medium",textAlign:R="center",fullWidth:B,dataPrimaryLink:L,tone:$,variant:O="secondary"}){const Y=fe(),Q=o||c,{mdUp:oe}=Bt(),U=w(G.Button,G.pressable,G[X("variant",O)],G[X("size",F)],G[X("textAlign",R)],B&&G.fullWidth,M&&G.disclosure,T&&t&&G.iconWithText,T&&t==null&&G.iconOnly,Q&&G.disabled,c&&G.loading,d&&!o&&!r&&G.pressed,H&&G.removeUnderline,$&&G[X("tone",$)]),J=M?n.createElement("span",{className:c?G.hidden:G.Icon},n.createElement(me,{source:c?"placeholder":ho(M,Ft,Lt)})):null,ae=fo(T)?n.createElement(me,{source:c?"placeholder":T}):T,K=ae?n.createElement("span",{className:c?G.hidden:G.Icon},ae):null,ce=["plain","monochromePlain"].includes(O);let he="medium";ce?he="regular":O==="primary"&&(he=oe?"medium":"semibold");let V="bodySm";(F==="large"||ce&&F!=="micro")&&(V="bodyMd");const Ee=t?n.createElement(W,{as:"span",variant:V,fontWeight:he,key:o?"text-disabled":"text"},t):null,ie=c?n.createElement("span",{className:G.Spinner},n.createElement($n,{size:"small",accessibilityLabel:Y.translate("Polaris.Button.spinnerAccessibilityLabel")})):null,ne={id:e,className:U,accessibilityLabel:p,ariaDescribedBy:b,role:h,onClick:E,onFocus:I,onBlur:S,onMouseUp:ut,onMouseEnter:C,onTouchStart:g,"data-primary-link":L},re={url:r,external:a,download:i,target:s},ue={submit:l,disabled:Q,loading:c,ariaControls:f,ariaExpanded:m,ariaChecked:P,pressed:d,onKeyDown:y,onKeyUp:N,onKeyPress:A,onPointerDown:v};return n.createElement(po,Object.assign({},ne,re,ue),ie,K,Ee,J)}function fo(e){return typeof e=="string"||typeof e=="object"&&e.body||typeof e=="function"}function ho(e,t,r){return e==="select"?Fn:e==="up"?t:r}function Dn({content:e,onAction:t,plain:r,destructive:o,...a},i,s){const l=r?"plain":void 0,c=o?"primary":void 0,d=!(i!=null&&i.tone)&&o?"critical":i==null?void 0:i.tone;return n.createElement(Ne,Object.assign({key:s,onClick:t,tone:d,variant:l||c},a,i),e)}var go={ShadowBevel:"Polaris-ShadowBevel"};function bo(e){const{as:t="div",bevel:r=!0,borderRadius:o,boxShadow:a,children:i,zIndex:s="0"}=e,l=t;return n.createElement(l,{className:go.ShadowBevel,style:{"--pc-shadow-bevel-z-index":s,...it("shadow-bevel","content",St(r,c=>c?'""':"none")),...it("shadow-bevel","box-shadow",St(r,c=>c?`var(--p-shadow-${a})`:"none")),...it("shadow-bevel","border-radius",St(r,c=>c?`var(--p-border-radius-${o})`:"var(--p-border-radius-0)"))}},i)}function St(e,t){return typeof e=="boolean"?t(e):Object.fromEntries(Object.entries(e).map(([r,o])=>[r,t(o)]))}var et={listReset:"Polaris-Box--listReset",Box:"Polaris-Box",visuallyHidden:"Polaris-Box--visuallyHidden",printHidden:"Polaris-Box--printHidden"};const z=u.forwardRef(({as:e="div",background:t,borderColor:r,borderStyle:o,borderWidth:a,borderBlockStartWidth:i,borderBlockEndWidth:s,borderInlineStartWidth:l,borderInlineEndWidth:c,borderRadius:d,borderEndStartRadius:p,borderEndEndRadius:h,borderStartStartRadius:f,borderStartEndRadius:m,children:b,color:P,id:E,minHeight:I,minWidth:S,maxWidth:y,overflowX:A,overflowY:N,outlineColor:C,outlineStyle:g,outlineWidth:v,padding:T,paddingBlock:M,paddingBlockStart:H,paddingBlockEnd:F,paddingInline:R,paddingInlineStart:B,paddingInlineEnd:L,role:$,shadow:O,tabIndex:Y,width:Q,printHidden:oe,visuallyHidden:U,position:J,insetBlockStart:ae,insetBlockEnd:K,insetInlineStart:ce,insetInlineEnd:he,zIndex:V,opacity:Ee,...ie},ne)=>{const re=o||(r||a||i||s||l||c?"solid":void 0),ue=g||(C||v?"solid":void 0),ke={"--pc-box-color":P?`var(--p-color-${P})`:void 0,"--pc-box-background":t?`var(--p-color-${t})`:void 0,"--pc-box-border-color":r?r==="transparent"?"transparent":`var(--p-color-${r})`:void 0,"--pc-box-border-style":re,"--pc-box-border-radius":d?`var(--p-border-radius-${d})`:void 0,"--pc-box-border-end-start-radius":p?`var(--p-border-radius-${p})`:void 0,"--pc-box-border-end-end-radius":h?`var(--p-border-radius-${h})`:void 0,"--pc-box-border-start-start-radius":f?`var(--p-border-radius-${f})`:void 0,"--pc-box-border-start-end-radius":m?`var(--p-border-radius-${m})`:void 0,"--pc-box-border-width":a?`var(--p-border-width-${a})`:void 0,"--pc-box-border-block-start-width":i?`var(--p-border-width-${i})`:void 0,"--pc-box-border-block-end-width":s?`var(--p-border-width-${s})`:void 0,"--pc-box-border-inline-start-width":l?`var(--p-border-width-${l})`:void 0,"--pc-box-border-inline-end-width":c?`var(--p-border-width-${c})`:void 0,"--pc-box-min-height":I,"--pc-box-min-width":S,"--pc-box-max-width":y,"--pc-box-outline-color":C?`var(--p-color-${C})`:void 0,"--pc-box-outline-style":ue,"--pc-box-outline-width":v?`var(--p-border-width-${v})`:void 0,"--pc-box-overflow-x":A,"--pc-box-overflow-y":N,...be("box","padding-block-start","space",H||M||T),...be("box","padding-block-end","space",F||M||T),...be("box","padding-inline-start","space",B||R||T),...be("box","padding-inline-end","space",L||R||T),"--pc-box-shadow":O?`var(--p-shadow-${O})`:void 0,"--pc-box-width":Q,position:J,"--pc-box-inset-block-start":ae?`var(--p-space-${ae})`:void 0,"--pc-box-inset-block-end":K?`var(--p-space-${K})`:void 0,"--pc-box-inset-inline-start":ce?`var(--p-space-${ce})`:void 0,"--pc-box-inset-inline-end":he?`var(--p-space-${he})`:void 0,zIndex:V,opacity:Ee},Ye=w(et.Box,U&&et.visuallyHidden,oe&&et.printHidden,e==="ul"&&et.listReset);return n.createElement(e,{className:Ye,id:E,ref:ne,style:Mt(ke),role:$,tabIndex:Y,...ie},b)});z.displayName="Box";const Da=({children:e,background:t="bg-surface",padding:r={xs:"400"},roundedAbove:o="sm"})=>{const a=Bt(),i="300",s=!!a[`${o}Up`];return n.createElement(to.Provider,{value:!0},n.createElement(bo,{boxShadow:"100",borderRadius:s?i:"0",zIndex:"32"},n.createElement(z,{background:t,padding:r,overflowX:"clip",overflowY:"clip",minHeight:"100%"},e)))};var vo={InlineStack:"Polaris-InlineStack"};const Ze=function({as:t="div",align:r,direction:o="row",blockAlign:a,gap:i,wrap:s=!0,children:l}){const c={"--pc-inline-stack-align":r,"--pc-inline-stack-block-align":a,"--pc-inline-stack-wrap":s?"wrap":"nowrap",...be("inline-stack","gap","space",i),...it("inline-stack","flex-direction",o)};return n.createElement(t,{className:vo.InlineStack,style:c},l)};var Ct={BlockStack:"Polaris-BlockStack",listReset:"Polaris-BlockStack--listReset",fieldsetReset:"Polaris-BlockStack--fieldsetReset"};const Po=({as:e="div",children:t,align:r,inlineAlign:o,gap:a,id:i,reverseOrder:s=!1,...l})=>{const c=w(Ct.BlockStack,(e==="ul"||e==="ol")&&Ct.listReset,e==="fieldset"&&Ct.fieldsetReset),d={"--pc-block-stack-align":r?`${r}`:null,"--pc-block-stack-inline-align":o?`${o}`:null,"--pc-block-stack-order":s?"column-reverse":"column",...be("block-stack","gap","space",a)};return n.createElement(e,{className:c,id:i,style:Mt(d),...l},t)},zn=u.createContext(!1);function Eo({children:e,filterActions:t}){return n.createElement(zn.Provider,{value:t},e)}var se={Item:"Polaris-ActionList__Item",default:"Polaris-ActionList--default",active:"Polaris-ActionList--active",destructive:"Polaris-ActionList--destructive",disabled:"Polaris-ActionList--disabled",Prefix:"Polaris-ActionList__Prefix",Suffix:"Polaris-ActionList__Suffix",indented:"Polaris-ActionList--indented",menu:"Polaris-ActionList--menu",Text:"Polaris-ActionList__Text"};const xo=u.createContext(!1);var Ae={Badge:"Polaris-Badge",toneSuccess:"Polaris-Badge--toneSuccess","toneSuccess-strong":"Polaris-Badge__toneSuccess--strong",toneInfo:"Polaris-Badge--toneInfo","toneInfo-strong":"Polaris-Badge__toneInfo--strong",toneAttention:"Polaris-Badge--toneAttention","toneAttention-strong":"Polaris-Badge__toneAttention--strong",toneWarning:"Polaris-Badge--toneWarning","toneWarning-strong":"Polaris-Badge__toneWarning--strong",toneCritical:"Polaris-Badge--toneCritical","toneCritical-strong":"Polaris-Badge__toneCritical--strong",toneNew:"Polaris-Badge--toneNew",toneMagic:"Polaris-Badge--toneMagic","toneRead-only":"Polaris-Badge__toneRead--only",toneEnabled:"Polaris-Badge--toneEnabled",sizeLarge:"Polaris-Badge--sizeLarge",withinFilter:"Polaris-Badge--withinFilter",Icon:"Polaris-Badge__Icon",PipContainer:"Polaris-Badge__PipContainer"};let te;(function(e){e.Info="info",e.Success="success",e.Warning="warning",e.Critical="critical",e.Attention="attention",e.New="new",e.Magic="magic",e.InfoStrong="info-strong",e.SuccessStrong="success-strong",e.WarningStrong="warning-strong",e.CriticalStrong="critical-strong",e.AttentionStrong="attention-strong",e.ReadOnly="read-only",e.Enabled="enabled"})(te||(te={}));let ze;(function(e){e.Incomplete="incomplete",e.PartiallyComplete="partiallyComplete",e.Complete="complete"})(ze||(ze={}));function jn(e,t,r){let o="",a="";if(!t&&!r)return"";switch(t){case ze.Incomplete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.incomplete");break;case ze.PartiallyComplete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.partiallyComplete");break;case ze.Complete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.complete");break}switch(r){case te.Info:case te.InfoStrong:a=e.translate("Polaris.Badge.TONE_LABELS.info");break;case te.Success:case te.SuccessStrong:a=e.translate("Polaris.Badge.TONE_LABELS.success");break;case te.Warning:case te.WarningStrong:a=e.translate("Polaris.Badge.TONE_LABELS.warning");break;case te.Critical:case te.CriticalStrong:a=e.translate("Polaris.Badge.TONE_LABELS.critical");break;case te.Attention:case te.AttentionStrong:a=e.translate("Polaris.Badge.TONE_LABELS.attention");break;case te.New:a=e.translate("Polaris.Badge.TONE_LABELS.new");break;case te.ReadOnly:a=e.translate("Polaris.Badge.TONE_LABELS.readOnly");break;case te.Enabled:a=e.translate("Polaris.Badge.TONE_LABELS.enabled");break}return!r&&t?o:r&&!t?a:e.translate("Polaris.Badge.progressAndTone",{progressLabel:o,toneLabel:a})}var It={Pip:"Polaris-Badge-Pip",toneInfo:"Polaris-Badge-Pip--toneInfo",toneSuccess:"Polaris-Badge-Pip--toneSuccess",toneNew:"Polaris-Badge-Pip--toneNew",toneAttention:"Polaris-Badge-Pip--toneAttention",toneWarning:"Polaris-Badge-Pip--toneWarning",toneCritical:"Polaris-Badge-Pip--toneCritical",progressIncomplete:"Polaris-Badge-Pip--progressIncomplete",progressPartiallyComplete:"Polaris-Badge-Pip--progressPartiallyComplete",progressComplete:"Polaris-Badge-Pip--progressComplete"};function So({tone:e,progress:t="complete",accessibilityLabelOverride:r}){const o=fe(),a=w(It.Pip,e&&It[X("tone",e)],t&&It[X("progress",t)]),i=r||jn(o,t,e);return n.createElement("span",{className:a},n.createElement(W,{as:"span",visuallyHidden:!0},i))}const an="medium",Co={complete:()=>n.createElement("svg",{viewBox:"0 0 20 20"},n.createElement("path",{d:"M6 10c0-.93 0-1.395.102-1.776a3 3 0 0 1 2.121-2.122C8.605 6 9.07 6 10 6c.93 0 1.395 0 1.776.102a3 3 0 0 1 2.122 2.122C14 8.605 14 9.07 14 10s0 1.395-.102 1.777a3 3 0 0 1-2.122 2.12C11.395 14 10.93 14 10 14s-1.395 0-1.777-.102a3 3 0 0 1-2.12-2.121C6 11.395 6 10.93 6 10Z"})),partiallyComplete:()=>n.createElement("svg",{viewBox:"0 0 20 20"},n.createElement("path",{fillRule:"evenodd",d:"m8.888 6.014-.017-.018-.02.02c-.253.013-.45.038-.628.086a3 3 0 0 0-2.12 2.122C6 8.605 6 9.07 6 10s0 1.395.102 1.777a3 3 0 0 0 2.121 2.12C8.605 14 9.07 14 10 14c.93 0 1.395 0 1.776-.102a3 3 0 0 0 2.122-2.121C14 11.395 14 10.93 14 10c0-.93 0-1.395-.102-1.776a3 3 0 0 0-2.122-2.122C11.395 6 10.93 6 10 6c-.475 0-.829 0-1.112.014ZM8.446 7.34a1.75 1.75 0 0 0-1.041.94l4.314 4.315c.443-.2.786-.576.941-1.042L8.446 7.34Zm4.304 2.536L10.124 7.25c.908.001 1.154.013 1.329.06a1.75 1.75 0 0 1 1.237 1.237c.047.175.059.42.06 1.329ZM8.547 12.69c.182.05.442.06 1.453.06h.106L7.25 9.894V10c0 1.01.01 1.27.06 1.453a1.75 1.75 0 0 0 1.237 1.237Z"})),incomplete:()=>n.createElement("svg",{viewBox:"0 0 20 20"},n.createElement("path",{fillRule:"evenodd",d:"M8.547 12.69c.183.05.443.06 1.453.06s1.27-.01 1.453-.06a1.75 1.75 0 0 0 1.237-1.237c.05-.182.06-.443.06-1.453s-.01-1.27-.06-1.453a1.75 1.75 0 0 0-1.237-1.237c-.182-.05-.443-.06-1.453-.06s-1.27.01-1.453.06A1.75 1.75 0 0 0 7.31 8.547c-.05.183-.06.443-.06 1.453s.01 1.27.06 1.453a1.75 1.75 0 0 0 1.237 1.237ZM6.102 8.224C6 8.605 6 9.07 6 10s0 1.395.102 1.777a3 3 0 0 0 2.122 2.12C8.605 14 9.07 14 10 14s1.395 0 1.777-.102a3 3 0 0 0 2.12-2.121C14 11.395 14 10.93 14 10c0-.93 0-1.395-.102-1.776a3 3 0 0 0-2.121-2.122C11.395 6 10.93 6 10 6c-.93 0-1.395 0-1.776.102a3 3 0 0 0-2.122 2.122Z"}))};function Un({children:e,tone:t,progress:r,icon:o,size:a=an,toneAndProgressLabelOverride:i}){const s=fe(),l=u.useContext(xo),c=w(Ae.Badge,t&&Ae[X("tone",t)],a&&a!==an&&Ae[X("size",a)],l&&Ae.withinFilter),d=i||jn(s,r,t);let p=!!d&&n.createElement(W,{as:"span",visuallyHidden:!0},d);return r&&!o&&(p=n.createElement("span",{className:Ae.Icon},n.createElement(me,{accessibilityLabel:d,source:Co[r]}))),n.createElement("span",{className:c},p,o&&n.createElement("span",{className:Ae.Icon},n.createElement(me,{source:o})),e&&n.createElement(W,{as:"span",variant:"bodySm",fontWeight:t==="new"?"medium":void 0},e))}Un.Pip=So;function Ve(e){const[t,r]=u.useState(e);return{value:t,toggle:u.useCallback(()=>r(o=>!o),[]),setTrue:u.useCallback(()=>r(!0),[]),setFalse:u.useCallback(()=>r(!1),[])}}var sn={TooltipContainer:"Polaris-Tooltip__TooltipContainer",HasUnderline:"Polaris-Tooltip__HasUnderline"};function Io(){const e=u.useContext(Hr);if(!e)throw new Error("No ephemeral presence manager was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function To(){const e=u.useContext(In);if(!e)throw new Error("No portals manager was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function Gn({children:e,idPrefix:t="",onPortalCreated:r=Ao}){const o=Dr(),{container:a}=To(),i=u.useId(),s=t!==""?`${t}-${i}`:i;return u.useEffect(()=>{r()},[r]),a?Mr.createPortal(n.createElement(eo,{theme:Kr(o)?o:Cn,"data-portal-id":s},e),a):null}function Ao(){}var xe={TooltipOverlay:"Polaris-Tooltip-TooltipOverlay",Tail:"Polaris-Tooltip-TooltipOverlay__Tail",positionedAbove:"Polaris-Tooltip-TooltipOverlay--positionedAbove",measuring:"Polaris-Tooltip-TooltipOverlay--measuring",measured:"Polaris-Tooltip-TooltipOverlay--measured",instant:"Polaris-Tooltip-TooltipOverlay--instant",Content:"Polaris-Tooltip-TooltipOverlay__Content",default:"Polaris-Tooltip-TooltipOverlay--default",wide:"Polaris-Tooltip-TooltipOverlay--wide"};function wo(e,t,r,o,a,i,s,l=0){const c=e.top,d=c+e.height,p=e.top-l,h=a.height-e.top-e.height,f=t.height,m=r.activator+r.container,b=r.container,P=e.top-Math.max(o.top,0),E=a.top+Math.min(a.height,o.top+o.height)-(e.top+e.height),I=P>=b,S=E>=b,y=Math.min(p,f),A=Math.min(h,f),N=Math.min(p+e.height,f),C=Math.min(h+e.height,f),g=s?0:a.top,v={height:y-m,top:c+g-y,positioning:"above"},T={height:A-m,top:d+g,positioning:"below"},M={height:C-m,top:c+g,positioning:"cover"},H={height:N-m,top:c+g-y+e.height+m,positioning:"cover"};return i==="above"?(I||P>=E&&!S)&&(p>f||p>h)?v:T:i==="below"?(S||E>=P&&!I)&&(h>f||h>p)?T:v:i==="cover"?(S||E>=P&&!I)&&(h+e.height>f||h>p)?M:H:I&&S?p>h?v:T:P>b?v:T}function yo(e,t,r,o,a){const i=r.width-t.width;if(a==="left")return Math.min(i,Math.max(0,e.left-o.horizontal));if(a==="right"){const s=r.width-(e.left+e.width);return Math.min(i,Math.max(0,s-o.horizontal))}return Math.min(i,Math.max(0,e.center.x-t.width/2))}function No(e,t){const{center:r}=e;return r.y<t.top||r.y>t.top+t.height}function Bo(e,t=Zn()){const r=Math.max(e.top,0),o=Math.max(e.left,0),a=Math.min(e.top+e.height,t.height),i=Math.min(e.left+e.width,t.width);return new _t({top:r,left:o,height:a-r,width:i-o})}function Zn(){return new _t({top:window.scrollY,left:window.scrollX,height:window.innerHeight,width:document.body.clientWidth})}var Tt={PositionedOverlay:"Polaris-PositionedOverlay",fixed:"Polaris-PositionedOverlay--fixed",preventInteraction:"Polaris-PositionedOverlay--preventInteraction"};const ln=Symbol("unique_identifier");function ko(e){const t=u.useRef(ln);return t.current===ln&&(t.current=e()),t}function _o(e){const t=kt(),r=u.useRef(!1);if(t&&!r.current)return r.current=!0,e()}const Vn=u.createContext(void 0);var Ce={Scrollable:"Polaris-Scrollable",hasTopShadow:"Polaris-Scrollable--hasTopShadow",hasBottomShadow:"Polaris-Scrollable--hasBottomShadow",horizontal:"Polaris-Scrollable--horizontal",vertical:"Polaris-Scrollable--vertical",scrollbarWidthThin:"Polaris-Scrollable--scrollbarWidthThin",scrollbarWidthNone:"Polaris-Scrollable--scrollbarWidthNone",scrollbarWidthAuto:"Polaris-Scrollable--scrollbarWidthAuto",scrollbarGutterStable:"Polaris-Scrollable--scrollbarGutterStable","scrollbarGutterStableboth-edges":"Polaris-Scrollable__scrollbarGutterStableboth--edges"};function Mo(){const e=u.useRef(null),t=u.useContext(Vn);u.useEffect(()=>{!t||!e.current||t(e.current.offsetTop)},[t]);const r=u.useId();return n.createElement("a",{id:r,ref:e})}const cn=100,qn=2,Xn=u.forwardRef(({children:e,className:t,horizontal:r=!0,vertical:o=!0,shadow:a,hint:i,focusable:s,scrollbarWidth:l="thin",scrollbarGutter:c,onScrolledToBottom:d,...p},h)=>{const[f,m]=u.useState(!1),[b,P]=u.useState(!1),E=ko(()=>new Ur),I=u.useRef(null),S=u.useCallback((C,g={})=>{var M;const v=g.behavior||"smooth",T=Yn()?"auto":v;(M=I.current)==null||M.scrollTo({top:C,behavior:T})},[]),y=u.useRef();u.useImperativeHandle(h||y,()=>({scrollTo:S}));const A=u.useCallback(()=>{const C=I.current;C&&requestAnimationFrame(()=>{const{scrollTop:g,clientHeight:v,scrollHeight:T}=C,M=T>v,H=g>0,F=g+v>=T-qn;m(H),P(!F),M&&F&&d&&d()})},[d]);_o(()=>{A(),i&&requestAnimationFrame(()=>Lo(I.current))}),u.useEffect(()=>{var v;const C=I.current;if(!C)return;const g=zr(A,50,{trailing:!0});return(v=E.current)==null||v.setContainer(C),C.addEventListener("scroll",A),globalThis.addEventListener("resize",g),()=>{C.removeEventListener("scroll",A),globalThis.removeEventListener("resize",g)}},[E,A]);const N=w(t,Ce.Scrollable,o&&Ce.vertical,r&&Ce.horizontal,a&&f&&Ce.hasTopShadow,a&&b&&Ce.hasBottomShadow,l&&Ce[X("scrollbarWidth",l)],c&&Ce[X("scrollbarGutter",c.replace(" ",""))]);return n.createElement(Vn.Provider,{value:S},n.createElement(jr.Provider,{value:E.current},n.createElement("div",Object.assign({className:N},Tn.props,p,{ref:I,tabIndex:s?0:void 0}),e)))});Xn.displayName="Scrollable";function Yn(){try{return window.matchMedia("(prefers-reduced-motion: reduce)").matches}catch{return!1}}function Lo(e){if(!e||Yn())return;const t=e.scrollHeight-e.clientHeight,r=Math.min(cn,t)-qn,o=()=>{requestAnimationFrame(()=>{e.scrollTop>=r&&(e.removeEventListener("scroll",o),e.scrollTo({top:0,behavior:"smooth"}))})};e.addEventListener("scroll",o),e.scrollTo({top:cn,behavior:"smooth"})}const Fo=e=>{const t=e.closest(Tn.selector);return t instanceof HTMLElement?t:document},Be=Xn;Be.ScrollTo=Mo;Be.forNode=Fo;const un={childList:!0,subtree:!0,characterData:!0,attributeFilter:["style"]};class Qn extends u.PureComponent{constructor(t){super(t),this.state={measuring:!0,activatorRect:We(this.props.activator),right:void 0,left:void 0,top:0,height:0,width:null,positioning:"below",zIndex:null,outsideScrollableContainer:!1,lockPosition:!1,chevronOffset:0},this.overlay=null,this.scrollableContainers=[],this.overlayDetails=()=>{const{measuring:r,left:o,right:a,positioning:i,height:s,activatorRect:l,chevronOffset:c}=this.state;return{measuring:r,left:o,right:a,desiredHeight:s,positioning:i,activatorRect:l,chevronOffset:c}},this.setOverlay=r=>{this.overlay=r},this.setScrollableContainers=()=>{const r=[];let o=Be.forNode(this.props.activator);if(o)for(r.push(o);o!=null&&o.parentElement;)o=Be.forNode(o.parentElement),r.push(o);this.scrollableContainers=r},this.registerScrollHandlers=()=>{this.scrollableContainers.forEach(r=>{r.addEventListener("scroll",this.handleMeasurement)})},this.unregisterScrollHandlers=()=>{this.scrollableContainers.forEach(r=>{r.removeEventListener("scroll",this.handleMeasurement)})},this.handleMeasurement=()=>{const{lockPosition:r,top:o}=this.state;this.observer.disconnect(),this.setState(({left:a,top:i,right:s})=>({left:a,right:s,top:i,height:0,positioning:"below",measuring:!0}),()=>{if(this.overlay==null||this.firstScrollableContainer==null)return;const{activator:a,preferredPosition:i="below",preferredAlignment:s="center",onScrollOut:l,fullWidth:c,fixed:d,preferInputActivator:p=!0}=this.props,h=p&&a.querySelector("input")||a,f=We(h),m=We(this.overlay),b=Wo(this.firstScrollableContainer)?document.body:this.firstScrollableContainer,P=We(b),E=c||i==="cover"?new _t({...m,width:f.width}):m;b===document.body&&(P.height=document.body.scrollHeight);let I=0;const S=b.querySelector(`${Gr.selector}`);S&&(I=S.clientHeight);const y=this.overlay.firstElementChild&&this.overlay.firstChild instanceof HTMLElement?Oo(this.overlay.firstElementChild):{activator:0,container:0,horizontal:0},A=Zn(),N=Ro(a),C=N==null?N:N+1,g=wo(f,E,y,P,A,i,d,I),v=yo(f,E,A,y,s),T=f.center.x-v+y.horizontal*2;this.setState({measuring:!1,activatorRect:We(a),left:s!=="right"?v:void 0,right:s==="right"?v:void 0,top:r?o:g.top,lockPosition:!!d,height:g.height||0,width:c||i==="cover"?E.width:null,positioning:g.positioning,outsideScrollableContainer:l!=null&&No(f,Bo(P)),zIndex:C,chevronOffset:T},()=>{this.overlay&&(this.observer.observe(this.overlay,un),this.observer.observe(a,un))})})},this.observer=new MutationObserver(this.handleMeasurement)}componentDidMount(){this.setScrollableContainers(),this.scrollableContainers.length&&!this.props.fixed&&this.registerScrollHandlers(),this.handleMeasurement()}componentWillUnmount(){this.observer.disconnect(),this.scrollableContainers.length&&!this.props.fixed&&this.unregisterScrollHandlers()}componentDidUpdate(){const{outsideScrollableContainer:t,top:r}=this.state,{onScrollOut:o,active:a}=this.props;a&&o!=null&&r!==0&&t&&o()}render(){const{left:t,right:r,top:o,zIndex:a,width:i}=this.state,{render:s,fixed:l,preventInteraction:c,classNames:d,zIndexOverride:p}=this.props,h={top:o==null||isNaN(o)?void 0:o,left:t==null||isNaN(t)?void 0:t,right:r==null||isNaN(r)?void 0:r,width:i==null||isNaN(i)?void 0:i,zIndex:p||a||void 0},f=w(Tt.PositionedOverlay,l&&Tt.fixed,c&&Tt.preventInteraction,d);return n.createElement("div",{className:f,style:h,ref:this.setOverlay},n.createElement(ct,{event:"resize",handler:this.handleMeasurement}),s(this.overlayDetails()))}get firstScrollableContainer(){return this.scrollableContainers[0]??null}forceUpdatePosition(){requestAnimationFrame(this.handleMeasurement)}}function Oo(e){const t=window.getComputedStyle(e);return{activator:parseFloat(t.marginTop||"0"),container:parseFloat(t.marginBottom||"0"),horizontal:parseFloat(t.marginLeft||"0")}}function Ro(e){const t=e.closest(An.selector)||document.body,r=t===document.body?"auto":parseInt(window.getComputedStyle(t).zIndex||"0",10);return r==="auto"||isNaN(r)?null:r}function Wo(e){return e===document}const $o=n.createElement(n.Fragment,null,n.createElement("path",{d:"M18.829 8.171 11.862.921A3 3 0 0 0 7.619.838L0 8.171h1.442l6.87-6.612a2 2 0 0 1 2.83.055l6.3 6.557h1.387Z",fill:"var(--p-color-tooltip-tail-up-border-experimental)"}),n.createElement("path",{d:"M17.442 10.171h-16v-2l6.87-6.612a2 2 0 0 1 2.83.055l6.3 6.557v2Z",fill:"var(--p-color-bg-surface)"})),Ho=n.createElement(n.Fragment,null,n.createElement("path",{d:"m0 2 6.967 7.25a3 3 0 0 0 4.243.083L18.829 2h-1.442l-6.87 6.612a2 2 0 0 1-2.83-.055L1.387 2H0Z",fill:"var(--p-color-tooltip-tail-down-border-experimental)"}),n.createElement("path",{d:"M1.387 0h16v2l-6.87 6.612a2 2 0 0 1-2.83-.055L1.387 2V0Z",fill:"var(--p-color-bg-surface)"}));function Do({active:e,activator:t,preferredPosition:r="above",preventInteraction:o,id:a,children:i,accessibilityLabel:s,width:l,padding:c,borderRadius:d,zIndexOverride:p,instant:h}){const f=fe();return e?n.createElement(Qn,{active:e,activator:t,preferredPosition:r,preventInteraction:o,render:b,zIndexOverride:p}):null;function b(P){const{measuring:E,desiredHeight:I,positioning:S,chevronOffset:y}=P,A=w(xe.TooltipOverlay,E&&xe.measuring,!E&&xe.measured,h&&xe.instant,S==="above"&&xe.positionedAbove),N=w(xe.Content,l&&xe[l]),C=E?void 0:{minHeight:I},g={"--pc-tooltip-chevron-x-pos":`${y}px`,"--pc-tooltip-border-radius":d?`var(--p-border-radius-${d})`:void 0,"--pc-tooltip-padding":c&&c==="default"?"var(--p-space-100) var(--p-space-200)":`var(--p-space-${c})`};return n.createElement("div",Object.assign({style:g,className:A},An.props),n.createElement("svg",{className:xe.Tail,width:"19",height:"11",fill:"none"},S==="above"?Ho:$o),n.createElement("div",{id:a,role:"tooltip",className:N,style:{...C,...g},"aria-label":s?f.translate("Polaris.TooltipOverlay.accessibilityLabel",{label:s}):void 0},i))}}const zo=150;function qe({children:e,content:t,dismissOnMouseOut:r,active:o,hoverDelay:a,preferredPosition:i="above",activatorWrapper:s="span",accessibilityLabel:l,width:c="default",padding:d="default",borderRadius:p,zIndexOverride:h,hasUnderline:f,persistOnClick:m,onOpen:b,onClose:P}){const E=p||"200",I=s,{value:S,setTrue:y,setFalse:A}=Ve(!!o),{value:N,toggle:C}=Ve(!!o&&!!m),[g,v]=u.useState(null),{presenceList:T,addPresence:M,removePresence:H}=Io(),F=u.useId(),R=u.useRef(null),B=u.useRef(!1),[L,$]=u.useState(!o),O=u.useRef(null),Y=u.useRef(null),Q=u.useCallback(()=>{o!==!1&&y()},[o,y]);u.useEffect(()=>{const ne=(R.current?ao(R.current):null)||R.current;ne&&(ne.tabIndex=0,ne.setAttribute("aria-describedby",F),ne.setAttribute("data-polaris-tooltip-activator","true"))},[F,e]),u.useEffect(()=>()=>{O.current&&clearTimeout(O.current),Y.current&&clearTimeout(Y.current)},[]);const oe=u.useCallback(()=>{$(!T.tooltip&&!S),b==null||b(),M("tooltip")},[M,T.tooltip,b,S]),U=u.useCallback(()=>{P==null||P(),$(!1),Y.current=setTimeout(()=>{H("tooltip")},zo)},[H,P]),J=u.useCallback(ie=>{ie.key==="Escape"&&(U==null||U(),A(),m&&C())},[A,U,m,C]);u.useEffect(()=>{o===!1&&S&&(U(),A())},[o,S,U,A]);const ae=g?n.createElement(Gn,{idPrefix:"tooltip"},n.createElement(Do,{id:F,preferredPosition:i,activator:g,active:S,accessibilityLabel:l,onClose:jo,preventInteraction:r,width:c,padding:d,borderRadius:E,zIndexOverride:h,instant:!L},n.createElement(W,{as:"span",variant:"bodyMd"},t))):null,K=w(s==="div"&&sn.TooltipContainer,f&&sn.HasUnderline);return n.createElement(I,{onFocus:()=>{oe(),Q()},onBlur:()=>{U(),A(),m&&C()},onMouseLeave:V,onMouseOver:Ee,onMouseDown:m?C:void 0,ref:ce,onKeyUp:J,className:K},e,ae);function ce(ie){const ne=R;if(ie==null){ne.current=null,v(null);return}ie.firstElementChild instanceof HTMLElement&&v(ie.firstElementChild),ne.current=ie}function he(){B.current=!0,a&&!T.tooltip?O.current=setTimeout(()=>{oe(),Q()},a):(oe(),Q())}function V(){O.current&&(clearTimeout(O.current),O.current=null),B.current=!1,U(),N||A()}function Ee(){!B.current&&he()}}function jo(){}function Jn({id:e,badge:t,content:r,accessibilityLabel:o,helpText:a,url:i,onAction:s,onMouseEnter:l,icon:c,image:d,prefix:p,suffix:h,disabled:f,external:m,destructive:b,ellipsis:P,truncate:E,active:I,role:S,variant:y="default"}){const A=w(se.Item,f&&se.disabled,b&&se.destructive,I&&se.active,y==="default"&&se.default,y==="indented"&&se.indented,y==="menu"&&se.menu);let N=null;p?N=n.createElement("span",{className:se.Prefix},p):c?N=n.createElement("span",{className:se.Prefix},n.createElement(me,{source:c})):d&&(N=n.createElement("span",{role:"presentation",className:se.Prefix,style:{backgroundImage:`url(${d}`}}));let C=r||"";E&&r?C=n.createElement(Uo,null,r):P&&(C=`${r}…`);const g=a?n.createElement(n.Fragment,null,n.createElement(z,null,C),n.createElement(W,{as:"span",variant:"bodySm",tone:I||f?void 0:"subdued"},a)):n.createElement(W,{as:"span",variant:"bodyMd",fontWeight:I?"semibold":"regular"},C),v=t&&n.createElement("span",{className:se.Suffix},n.createElement(Un,{tone:t.tone},t.content)),T=h&&n.createElement(z,null,n.createElement("span",{className:se.Suffix},h)),M=n.createElement("span",{className:se.Text},n.createElement(W,{as:"span",variant:"bodyMd",fontWeight:I?"semibold":"regular"},g)),H=n.createElement(Ze,{blockAlign:"center",gap:"150",wrap:!1},N,M,v,T),F=n.createElement(z,{width:"100%"},H),R=I?n.createElement(Be.ScrollTo,null):null,B=i?n.createElement(Hn,{id:e,url:f?null:i,className:A,external:m,"aria-label":o,onClick:f?null:s,role:S},F):n.createElement("button",{id:e,type:"button",className:A,disabled:f,"aria-label":o,onClick:s,onMouseUp:ut,role:S,onMouseEnter:l},F);return n.createElement(n.Fragment,null,R,B)}const Uo=({children:e})=>{const t=Zr(),r=u.useRef(null),[o,a]=u.useState(!1);return Ge(()=>{r.current&&a(r.current.scrollWidth>r.current.offsetWidth)},[e]),o?n.createElement(qe,{zIndexOverride:Number(t.zIndex["z-index-11"]),preferredPosition:"above",hoverDelay:1e3,content:e,dismissOnMouseOut:!0},n.createElement(W,{as:"span",truncate:!0},e)):n.createElement(W,{as:"span",truncate:!0},n.createElement(z,{width:"100%",ref:r},e))};function Go({section:e,hasMultipleSections:t,isFirst:r,actionRole:o,onActionAnyItem:a}){const i=p=>()=>{p&&p(),a&&a()},s=e.items.map(({content:p,helpText:h,onAction:f,...m},b)=>{const P=n.createElement(Jn,Object.assign({content:p,helpText:h,role:o,onAction:i(f)},m));return n.createElement(z,{as:"li",key:`${p}-${b}`,role:o==="menuitem"?"presentation":void 0},n.createElement(Ze,{wrap:!1},P))});let l=null;e.title&&(l=typeof e.title=="string"?n.createElement(z,{paddingBlockStart:"300",paddingBlockEnd:"100",paddingInlineStart:"300",paddingInlineEnd:"300"},n.createElement(W,{as:"p",variant:"headingSm"},e.title)):n.createElement(z,{padding:"200",paddingInlineEnd:"150"},e.title));let c;switch(o){case"option":c="presentation";break;case"menuitem":c=t?"presentation":"menu";break;default:c=void 0;break}const d=n.createElement(n.Fragment,null,l,n.createElement(z,Object.assign({as:"div",padding:"150"},t&&{paddingBlockStart:"0"},{tabIndex:t?void 0:-1}),n.createElement(Po,Object.assign({gap:"050",as:"ul"},c&&{role:c}),s)));return t?n.createElement(z,Object.assign({as:"li",role:"presentation",borderColor:"border-secondary"},!r&&{borderBlockStartWidth:"025"},!e.title&&{paddingBlockStart:"150"}),d):d}function Xe({keyCode:e,handler:t,keyEvent:r="keyup",options:o,useCapture:a}){const i=u.useRef({handler:t,keyCode:e});Ge(()=>{i.current={handler:t,keyCode:e}},[t,e]);const s=u.useCallback(l=>{const{handler:c,keyCode:d}=i.current;l.keyCode===d&&c(l)},[]);return u.useEffect(()=>(document.addEventListener(r,s,a||o),()=>{document.removeEventListener(r,s,a||o)}),[r,s,a,o]),null}var k={TextField:"Polaris-TextField",ClearButton:"Polaris-TextField__ClearButton",Loading:"Polaris-TextField__Loading",disabled:"Polaris-TextField--disabled",error:"Polaris-TextField--error",readOnly:"Polaris-TextField--readOnly",Input:"Polaris-TextField__Input",Backdrop:"Polaris-TextField__Backdrop",multiline:"Polaris-TextField--multiline",hasValue:"Polaris-TextField--hasValue",focus:"Polaris-TextField--focus",VerticalContent:"Polaris-TextField__VerticalContent",InputAndSuffixWrapper:"Polaris-TextField__InputAndSuffixWrapper",toneMagic:"Polaris-TextField--toneMagic",Prefix:"Polaris-TextField__Prefix",Suffix:"Polaris-TextField__Suffix",AutoSizeWrapper:"Polaris-TextField__AutoSizeWrapper",AutoSizeWrapperWithSuffix:"Polaris-TextField__AutoSizeWrapperWithSuffix",suggestion:"Polaris-TextField--suggestion",borderless:"Polaris-TextField--borderless",slim:"Polaris-TextField--slim","Input-hasClearButton":"Polaris-TextField__Input--hasClearButton","Input-suffixed":"Polaris-TextField__Input--suffixed","Input-alignRight":"Polaris-TextField__Input--alignRight","Input-alignLeft":"Polaris-TextField__Input--alignLeft","Input-alignCenter":"Polaris-TextField__Input--alignCenter","Input-autoSize":"Polaris-TextField__Input--autoSize",PrefixIcon:"Polaris-TextField__PrefixIcon",CharacterCount:"Polaris-TextField__CharacterCount",AlignFieldBottom:"Polaris-TextField__AlignFieldBottom",Spinner:"Polaris-TextField__Spinner",SpinnerIcon:"Polaris-TextField__SpinnerIcon",Resizer:"Polaris-TextField__Resizer",DummyInput:"Polaris-TextField__DummyInput",Segment:"Polaris-TextField__Segment",monospaced:"Polaris-TextField--monospaced"};const Zo=n.forwardRef(function({onChange:t,onClick:r,onMouseDown:o,onMouseUp:a,onBlur:i},s){function l(d){return()=>t(d)}function c(d){return p=>{p.button===0&&(o==null||o(d))}}return n.createElement("div",{className:k.Spinner,onClick:r,"aria-hidden":!0,ref:s},n.createElement("div",{role:"button",className:k.Segment,tabIndex:-1,onClick:l(1),onMouseDown:c(l(1)),onMouseUp:a,onBlur:i},n.createElement("div",{className:k.SpinnerIcon},n.createElement(me,{source:Ft}))),n.createElement("div",{role:"button",className:k.Segment,tabIndex:-1,onClick:l(-1),onMouseDown:c(l(-1)),onMouseUp:a,onBlur:i},n.createElement("div",{className:k.SpinnerIcon},n.createElement(me,{source:Lt}))))});var Ie={hidden:"Polaris-Labelled--hidden",LabelWrapper:"Polaris-Labelled__LabelWrapper",disabled:"Polaris-Labelled--disabled",HelpText:"Polaris-Labelled__HelpText",readOnly:"Polaris-Labelled--readOnly",Error:"Polaris-Labelled__Error",Action:"Polaris-Labelled__Action"},dn={InlineError:"Polaris-InlineError",Icon:"Polaris-InlineError__Icon"};function Vo({message:e,fieldID:t}){return e?n.createElement("div",{id:qo(t),className:dn.InlineError},n.createElement("div",{className:dn.Icon},n.createElement(me,{source:Nn})),n.createElement(W,{as:"span",variant:"bodyMd"},e)):null}function qo(e){return`${e}Error`}var tt={Label:"Polaris-Label",hidden:"Polaris-Label--hidden",Text:"Polaris-Label__Text",RequiredIndicator:"Polaris-Label__RequiredIndicator"};function Kn(e){return`${e}Label`}function Xo({children:e,id:t,hidden:r,requiredIndicator:o}){const a=w(tt.Label,r&&tt.hidden);return n.createElement("div",{className:a},n.createElement("label",{id:Kn(t),htmlFor:t,className:w(tt.Text,o&&tt.RequiredIndicator)},n.createElement(W,{as:"span",variant:"bodyMd"},e)))}function Yo({id:e,label:t,error:r,action:o,helpText:a,children:i,labelHidden:s,requiredIndicator:l,disabled:c,readOnly:d,...p}){const h=w(s&&Ie.hidden,c&&Ie.disabled,d&&Ie.readOnly),f=o?n.createElement("div",{className:Ie.Action},Dn(o,{variant:"plain"})):null,m=a?n.createElement("div",{className:Ie.HelpText,id:er(e),"aria-disabled":c},n.createElement(W,{as:"span",tone:"subdued",variant:"bodyMd",breakWord:!0},a)):null,b=r&&typeof r!="boolean"&&n.createElement("div",{className:Ie.Error},n.createElement(Vo,{message:r,fieldID:e})),P=t?n.createElement("div",{className:Ie.LabelWrapper},n.createElement(Xo,Object.assign({id:e,requiredIndicator:l},p,{hidden:!1}),t),f):null;return n.createElement("div",{className:h},P,i,b,m)}function er(e){return`${e}HelpText`}var De={Connected:"Polaris-Connected",Item:"Polaris-Connected__Item","Item-primary":"Polaris-Connected__Item--primary","Item-focused":"Polaris-Connected__Item--focused"};function At({children:e,position:t}){const{value:r,setTrue:o,setFalse:a}=Ve(!1),i=w(De.Item,r&&De["Item-focused"],t==="primary"?De["Item-primary"]:De["Item-connection"]);return n.createElement("div",{onBlur:a,onFocus:o,className:i},e)}function Qo({children:e,left:t,right:r}){const o=t?n.createElement(At,{position:"left"},t):null,a=r?n.createElement(At,{position:"right"},r):null;return n.createElement("div",{className:De.Connected},o,n.createElement(At,{position:"primary"},e),a)}function Jo({contents:e,currentHeight:t=null,minimumLines:r,onHeightChange:o}){const a=u.useRef(null),i=u.useRef(null),s=u.useRef(),l=u.useRef(t);t!==l.current&&(l.current=t),u.useEffect(()=>()=>{s.current&&cancelAnimationFrame(s.current)},[]);const c=r?n.createElement("div",{ref:i,className:k.DummyInput,dangerouslySetInnerHTML:{__html:ta(r)}}):null,d=u.useCallback(()=>{s.current&&cancelAnimationFrame(s.current),s.current=requestAnimationFrame(()=>{if(!a.current||!i.current)return;const p=Math.max(a.current.offsetHeight,i.current.offsetHeight);p!==l.current&&o(p)})},[o]);return Ge(()=>{d()}),n.createElement("div",{"aria-hidden":!0,className:k.Resizer},n.createElement(ct,{event:"resize",handler:d}),n.createElement("div",{ref:a,className:k.DummyInput,dangerouslySetInnerHTML:{__html:na(e)}}),c)}const tr={"&":"&amp;","<":"&lt;",">":"&gt;","\n":"<br>","\r":""},Ko=new RegExp(`[${Object.keys(tr).join()}]`,"g");function ea(e){return tr[e]}function ta(e){let t="";for(let r=0;r<e;r++)t+="<br>";return t}function na(e){return e?`${e.replace(Ko,ea)}<br>`:"<br>"}function ra({prefix:e,suffix:t,verticalContent:r,placeholder:o,value:a="",helpText:i,label:s,labelAction:l,labelHidden:c,disabled:d,clearButton:p,readOnly:h,autoFocus:f,focused:m,multiline:b,error:P,connectedRight:E,connectedLeft:I,type:S="text",name:y,id:A,role:N,step:C,largeStep:g,autoComplete:v,max:T,maxLength:M,maxHeight:H,min:F,minLength:R,pattern:B,inputMode:L,spellCheck:$,ariaOwns:O,ariaControls:Y,ariaExpanded:Q,ariaActiveDescendant:oe,ariaAutocomplete:U,showCharacterCount:J,align:ae,requiredIndicator:K,monospaced:ce,selectTextOnFocus:he,suggestion:V,variant:Ee="inherit",size:ie="medium",onClearButtonClick:ne,onChange:re,onSpinnerChange:ue,onFocus:ke,onBlur:Ye,tone:Wt,autoSize:dt,loading:lr}){const pt=fe(),[mt,cr]=u.useState(null),[_e,ft]=u.useState(!!m),ur=kt(),dr=u.useId(),D=A??dr,$t=u.useRef(null),ve=u.useRef(null),Ht=u.useRef(null),ht=u.useRef(null),gt=u.useRef(null),bt=u.useRef(null),Qe=u.useRef(null),vt=u.useRef(),Pt=u.useRef(null),Te=u.useCallback(()=>b?Ht.current:ve.current,[b]);u.useEffect(()=>{const x=Te();!x||m===void 0||(m?x.focus():x.blur())},[m,r,Te]),u.useEffect(()=>{const x=ve.current;!x||!(S==="text"||S==="tel"||S==="search"||S==="url"||S==="password")||!V||x.setSelectionRange(a.length,V.length)},[_e,a,S,V]);const Me=V||a,Dt=C??1,zt=T??1/0,jt=F??-1/0,pr=w(k.TextField,!!Me&&k.hasValue,d&&k.disabled,h&&k.readOnly,P&&k.error,Wt&&k[X("tone",Wt)],b&&k.multiline,_e&&!d&&k.focus,Ee!=="inherit"&&k[Ee],ie==="slim"&&k.slim),mr=S==="currency"?"text":S,Je=S==="number"||S==="integer",fr=n.isValidElement(e)&&e.type===me,hr=e?n.createElement("div",{className:w(k.Prefix,fr&&k.PrefixIcon),id:`${D}-Prefix`,ref:ht},n.createElement(W,{as:"span",variant:"bodyMd"},e)):null,Ut=t?n.createElement("div",{className:k.Suffix,id:`${D}-Suffix`,ref:gt},n.createElement(W,{as:"span",variant:"bodyMd"},t)):null,gr=lr?n.createElement("div",{className:k.Loading,id:`${D}-Loading`,ref:bt},n.createElement($n,{size:"small"})):null;let Gt=null;if(J){const x=Me.length,_=M?pt.translate("Polaris.TextField.characterCountWithMaxLength",{count:x,limit:M}):pt.translate("Polaris.TextField.characterCount",{count:x}),q=w(k.CharacterCount,b&&k.AlignFieldBottom),ee=M?`${x}/${M}`:x;Gt=n.createElement("div",{id:`${D}-CharacterCounter`,className:q,"aria-label":_,"aria-live":_e?"polite":"off","aria-atomic":"true",onClick:Ke},n.createElement(W,{as:"span",variant:"bodyMd"},ee))}const br=p&&Me!==""?n.createElement("button",{type:"button",className:k.ClearButton,onClick:Nr,disabled:d},n.createElement(W,{as:"span",visuallyHidden:!0},pt.translate("Polaris.Common.clear")),n.createElement(me,{source:On,tone:"base"})):null,Le=u.useCallback((x,_=Dt)=>{if(re==null&&ue==null)return;const q=_r=>(_r.toString().split(".")[1]||[]).length,ee=a?parseFloat(a):0;if(isNaN(ee))return;const ge=S==="integer"?0:Math.max(q(ee),q(_)),Re=Math.min(Number(zt),Math.max(ee+x*_,Number(jt)));ue!=null?ue(String(Re.toFixed(ge)),D):re!=null&&re(String(Re.toFixed(ge)),D)},[D,zt,jt,re,ue,Dt,S,a]),Et=u.useCallback(()=>{clearTimeout(vt.current)},[]),vr=u.useCallback(x=>{let ee=200;const ge=()=>{ee>50&&(ee-=10),x(0),vt.current=window.setTimeout(ge,ee)};vt.current=window.setTimeout(ge,ee),document.addEventListener("mouseup",Et,{once:!0})},[Et]),Pr=Je&&C!==0&&!d&&!h?n.createElement(Zo,{onClick:Ke,onChange:Le,onMouseDown:vr,onMouseUp:Et,ref:Pt,onBlur:Yt}):null,Er=b&&mt?{height:mt,maxHeight:H}:null,xr=u.useCallback(x=>{cr(x)},[]),Sr=b&&ur?n.createElement(Jo,{contents:Me||o,currentHeight:mt,minimumLines:typeof b=="number"?b:1,onHeightChange:xr}):null,Fe=[];P&&Fe.push(`${D}Error`),i&&Fe.push(er(D)),J&&Fe.push(`${D}-CharacterCounter`);const Oe=[];e&&Oe.push(`${D}-Prefix`),t&&Oe.push(`${D}-Suffix`),r&&Oe.push(`${D}-VerticalContent`),Oe.unshift(Kn(D));const Cr=w(k.Input,ae&&k[X("Input-align",ae)],t&&k["Input-suffixed"],p&&k["Input-hasClearButton"],ce&&k.monospaced,V&&k.suggestion,dt&&k["Input-autoSize"]),Zt=x=>{if(ft(!0),he&&!V){const _=Te();_==null||_.select()}ke&&ke(x)};yn("wheel",Ir,ve);function Ir(x){document.activeElement===x.target&&Je&&x.stopPropagation()}const Vt=u.createElement(b?"textarea":"input",{name:y,id:D,disabled:d,readOnly:h,role:N,autoFocus:f,value:Me,placeholder:o,style:Er,autoComplete:v,className:Cr,ref:b?Ht:ve,min:F,max:T,step:C,minLength:R,maxLength:M,spellCheck:$,pattern:B,inputMode:L,type:mr,rows:oa(b),size:dt?1:void 0,"aria-describedby":Fe.length?Fe.join(" "):void 0,"aria-labelledby":Oe.join(" "),"aria-invalid":!!P,"aria-owns":O,"aria-activedescendant":oe,"aria-autocomplete":U,"aria-controls":Y,"aria-expanded":Q,"aria-required":K,...aa(b),onFocus:Zt,onBlur:Yt,onClick:Ke,onKeyPress:Br,onKeyDown:kr,onChange:V?void 0:Xt,onInput:V?Xt:void 0,"data-1p-ignore":v==="off"||void 0,"data-lpignore":v==="off"||void 0,"data-form-type":v==="off"?"other":void 0}),Tr=r?n.createElement("div",{className:k.VerticalContent,id:`${D}-VerticalContent`,ref:Qe,onClick:Ke},r,Vt):null,qt=r?Tr:Vt,Ar=n.createElement("div",{className:w(k.Backdrop,I&&k["Backdrop-connectedLeft"],E&&k["Backdrop-connectedRight"])}),wr=dt?n.createElement("div",{className:k.InputAndSuffixWrapper},n.createElement("div",{className:w(k.AutoSizeWrapper,t&&k.AutoSizeWrapperWithSuffix),"data-auto-size-value":a||o},qt),Ut):n.createElement(n.Fragment,null,qt,Ut);return n.createElement(Yo,{label:s,id:D,error:P,action:l,labelHidden:c,helpText:i,requiredIndicator:K,disabled:d,readOnly:h},n.createElement(Qo,{left:I,right:E},n.createElement("div",{className:pr,onClick:yr,ref:$t},hr,wr,Gt,gr,br,Pr,Ar,Sr)));function Xt(x){re&&re(x.currentTarget.value,D)}function yr(x){var ee,ge,Re;const{target:_}=x,q=(ee=ve==null?void 0:ve.current)==null?void 0:ee.getAttribute("role");if(_===ve.current&&q==="combobox"){(ge=ve.current)==null||ge.focus(),Zt(x);return}Qt(_)||en(_)||xt(_)||Jt(_)||Kt(_)||_e||(Re=Te())==null||Re.focus()}function Ke(x){var _;!Jt(x.target)&&!xt(x.target)&&x.stopPropagation(),!(Qt(x.target)||en(x.target)||xt(x.target)||Kt(x.target)||_e)&&(ft(!0),(_=Te())==null||_.focus())}function Nr(){ne&&ne(D)}function Br(x){const{key:_,which:q}=x,ee=/[\d.,eE+-]$/,ge=/[\deE+-]$/;!Je||q===de.Enter||S==="number"&&ee.test(_)||S==="integer"&&ge.test(_)||x.preventDefault()}function kr(x){if(!Je)return;const{key:_,which:q}=x;S==="integer"&&(_==="ArrowUp"||q===de.UpArrow)&&(Le(1),x.preventDefault()),S==="integer"&&(_==="ArrowDown"||q===de.DownArrow)&&(Le(-1),x.preventDefault()),(q===de.Home||_==="Home")&&F!==void 0&&(ue!=null?ue(String(F),D):re!=null&&re(String(F),D)),(q===de.End||_==="End")&&T!==void 0&&(ue!=null?ue(String(T),D):re!=null&&re(String(T),D)),(q===de.PageUp||_==="PageUp")&&g!==void 0&&Le(1,g),(q===de.PageDown||_==="PageDown")&&g!==void 0&&Le(-1,g)}function Yt(x){var _;ft(!1),!((_=$t.current)!=null&&_.contains(x==null?void 0:x.relatedTarget))&&Ye&&Ye(x)}function xt(x){const _=Te();return x instanceof HTMLElement&&_&&(_.contains(x)||_.contains(document.activeElement))}function Qt(x){return x instanceof Element&&(ht.current&&ht.current.contains(x)||gt.current&&gt.current.contains(x))}function Jt(x){return x instanceof Element&&Pt.current&&Pt.current.contains(x)}function Kt(x){return x instanceof Element&&bt.current&&bt.current.contains(x)}function en(x){return x instanceof Element&&Qe.current&&(Qe.current.contains(x)||Qe.current.contains(document.activeElement))}}function oa(e){if(e)return typeof e=="number"?e:1}function aa(e){if(e)return e||typeof e=="number"&&e>0?{"aria-multiline":!0}:void 0}const ia=8;function Rt({items:e,sections:t=[],actionRole:r,allowFiltering:o,onActionAnyItem:a}){const i=fe(),s=u.useContext(zn);let l=[];const c=u.useRef(null),[d,p]=u.useState("");e?l=[{items:e},...t]:t&&(l=t);const h=l==null?void 0:l.some(g=>g.items.some(v=>typeof v.content=="string")),f=l.length>1,m=f&&r==="menuitem"?"menu":void 0,b=f&&r==="menuitem"?-1:void 0,P=l==null?void 0:l.map(g=>({...g,items:g.items.filter(({content:v})=>typeof v=="string"?v==null?void 0:v.toLowerCase().includes(d.toLowerCase()):v)})),E=P.map((g,v)=>g.items.length>0?n.createElement(Go,{key:typeof g.title=="string"?g.title:v,section:g,hasMultipleSections:f,actionRole:r,onActionAnyItem:a,isFirst:v===0}):null),I=g=>{g.preventDefault(),c.current&&g.target&&c.current.contains(g.target)&&so(c.current,g.target)},S=g=>{g.preventDefault(),c.current&&g.target&&c.current.contains(g.target)&&lo(c.current,g.target)},y=r==="menuitem"?n.createElement(n.Fragment,null,n.createElement(Xe,{keyEvent:"keydown",keyCode:de.DownArrow,handler:S}),n.createElement(Xe,{keyEvent:"keydown",keyCode:de.UpArrow,handler:I})):null,A=u.useMemo(()=>(P==null?void 0:P.reduce((v,T)=>v+T.items.length,0))||0,[P]),C=((l==null?void 0:l.reduce((g,v)=>g+v.items.length,0))||0)>=ia;return n.createElement(n.Fragment,null,(o||s)&&C&&h&&n.createElement(z,{padding:"200",paddingBlockEnd:A>0?"0":"200"},n.createElement(ra,{clearButton:!0,labelHidden:!0,label:i.translate("Polaris.ActionList.SearchField.placeholder"),placeholder:i.translate("Polaris.ActionList.SearchField.placeholder"),autoComplete:"off",value:d,onChange:g=>p(g),prefix:n.createElement(me,{source:Ln}),onClearButtonClick:()=>p("")})),n.createElement(z,{as:f?"ul":"div",ref:c,role:m,tabIndex:b},y,E))}Rt.Item=Jn;var pn={ActionMenu:"Polaris-ActionMenu"},sa={RollupActivator:"Polaris-ActionMenu-RollupActions__RollupActivator"};function la(e,{id:t,active:r=!1,ariaHaspopup:o,activatorDisabled:a=!1}){a||(e.tabIndex=e.tabIndex||0),e.setAttribute("aria-controls",t),e.setAttribute("aria-owns",t),e.setAttribute("aria-expanded",String(r)),e.setAttribute("data-state",r?"open":"closed"),o!=null&&e.setAttribute("aria-haspopup",String(o))}function nr(e,t,r){return e==null?null:rr(e,t)?e:n.createElement(t,r,e)}const ca=(e,t)=>e===t;function rr(e,t){var s;if(e==null||!u.isValidElement(e)||typeof e.type=="string")return!1;const{type:r}=e,a=((s=e.props)==null?void 0:s.__type__)||r;return(Array.isArray(t)?t:[t]).some(l=>typeof a!="string"&&ca(l,a))}function ua(e,t=()=>!0){return u.Children.toArray(e).filter(r=>u.isValidElement(r)&&t(r))}function da({condition:e,wrapper:t,children:r}){return e?t(r):r}function wt({condition:e,children:t}){return e?t:null}var j={Popover:"Polaris-Popover",PopoverOverlay:"Polaris-Popover__PopoverOverlay","PopoverOverlay-noAnimation":"Polaris-Popover__PopoverOverlay--noAnimation","PopoverOverlay-entering":"Polaris-Popover__PopoverOverlay--entering","PopoverOverlay-open":"Polaris-Popover__PopoverOverlay--open",measuring:"Polaris-Popover--measuring","PopoverOverlay-exiting":"Polaris-Popover__PopoverOverlay--exiting",fullWidth:"Polaris-Popover--fullWidth",Content:"Polaris-Popover__Content",positionedAbove:"Polaris-Popover--positionedAbove",positionedCover:"Polaris-Popover--positionedCover",ContentContainer:"Polaris-Popover__ContentContainer","Content-fullHeight":"Polaris-Popover__Content--fullHeight","Content-fluidContent":"Polaris-Popover__Content--fluidContent",Pane:"Polaris-Popover__Pane","Pane-fixed":"Polaris-Popover__Pane--fixed","Pane-subdued":"Polaris-Popover__Pane--subdued","Pane-captureOverscroll":"Polaris-Popover__Pane--captureOverscroll",Section:"Polaris-Popover__Section",FocusTracker:"Polaris-Popover__FocusTracker","PopoverOverlay-hideOnPrint":"Polaris-Popover__PopoverOverlay--hideOnPrint"};function or({children:e}){return n.createElement("div",{className:j.Section},n.createElement(z,{paddingInlineStart:"300",paddingInlineEnd:"300",paddingBlockStart:"200",paddingBlockEnd:"150"},e))}function Nt({captureOverscroll:e=!1,fixed:t,sectioned:r,children:o,height:a,subdued:i,onScrolledToBottom:s}){const l=w(j.Pane,t&&j["Pane-fixed"],i&&j["Pane-subdued"],e&&j["Pane-captureOverscroll"]),c=r?nr(o,or,{}):o,d=a?{height:a,maxHeight:a,minHeight:a}:void 0;return t?n.createElement("div",{style:d,className:l},c):n.createElement(Be,{shadow:!0,className:l,style:d,onScrolledToBottom:s,scrollbarWidth:"thin"},c)}let Pe;(function(e){e[e.Click=0]="Click",e[e.EscapeKeypress=1]="EscapeKeypress",e[e.FocusOut=2]="FocusOut",e[e.ScrollOut=3]="ScrollOut"})(Pe||(Pe={}));var le;(function(e){e.Entering="entering",e.Entered="entered",e.Exiting="exiting",e.Exited="exited"})(le||(le={}));class ar extends u.PureComponent{constructor(t){super(t),this.state={transitionStatus:this.props.active?le.Entering:le.Exited},this.contentNode=u.createRef(),this.renderPopover=r=>{const{measuring:o,desiredHeight:a,positioning:i}=r,{id:s,children:l,sectioned:c,fullWidth:d,fullHeight:p,fluidContent:h,hideOnPrint:f,autofocusTarget:m,captureOverscroll:b}=this.props,P=i==="cover",E=w(j.Popover,o&&j.measuring,(d||P)&&j.fullWidth,f&&j["PopoverOverlay-hideOnPrint"],i&&j[X("positioned",i)]),I=o?void 0:{height:a},S=w(j.Content,p&&j["Content-fullHeight"],h&&j["Content-fluidContent"]);return n.createElement("div",Object.assign({className:E},Vr.props),n.createElement(ct,{event:"click",handler:this.handleClick}),n.createElement(ct,{event:"touchstart",handler:this.handleClick}),n.createElement(Xe,{keyCode:de.Escape,handler:this.handleEscape}),n.createElement("div",{className:j.FocusTracker,tabIndex:0,onFocus:this.handleFocusFirstItem}),n.createElement("div",{className:j.ContentContainer},n.createElement("div",{id:s,tabIndex:m==="none"?void 0:-1,className:S,style:I,ref:this.contentNode},pa(l,{captureOverscroll:b,sectioned:c}))),n.createElement("div",{className:j.FocusTracker,tabIndex:0,onFocus:this.handleFocusLastItem}))},this.handleClick=r=>{const o=r.target,{contentNode:a,props:{activator:i,onClose:s,preventCloseOnChildOverlayClick:l}}=this,c=r.composedPath(),d=l?ma(c,this.context.container):fn(c,a),p=mn(i,o);d||p||this.state.transitionStatus!==le.Entered||s(Pe.Click)},this.handleScrollOut=()=>{this.props.onClose(Pe.ScrollOut)},this.handleEscape=r=>{const o=r.target,{contentNode:a,props:{activator:i}}=this,s=r.composedPath(),l=fn(s,a),c=mn(i,o);(l||c)&&this.props.onClose(Pe.EscapeKeypress)},this.handleFocusFirstItem=()=>{this.props.onClose(Pe.FocusOut)},this.handleFocusLastItem=()=>{this.props.onClose(Pe.FocusOut)},this.overlayRef=u.createRef()}forceUpdatePosition(){var t;(t=this.overlayRef.current)==null||t.forceUpdatePosition()}changeTransitionStatus(t,r){this.setState({transitionStatus:t},r),this.contentNode.current&&this.contentNode.current.getBoundingClientRect()}componentDidMount(){this.props.active&&(this.focusContent(),this.changeTransitionStatus(le.Entered))}componentDidUpdate(t){this.props.active&&!t.active&&(this.focusContent(),this.changeTransitionStatus(le.Entering,()=>{this.clearTransitionTimeout(),this.enteringTimer=window.setTimeout(()=>{this.setState({transitionStatus:le.Entered})},parseInt(qr.motion["motion-duration-100"],10))})),!this.props.active&&t.active&&(this.clearTransitionTimeout(),this.setState({transitionStatus:le.Exited}))}componentWillUnmount(){this.clearTransitionTimeout()}render(){const{active:t,activator:r,fullWidth:o,preferredPosition:a="below",preferredAlignment:i="center",preferInputActivator:s=!0,fixed:l,zIndexOverride:c}=this.props,{transitionStatus:d}=this.state;if(d===le.Exited&&!t)return null;const p=w(j.PopoverOverlay,d===le.Entering&&j["PopoverOverlay-entering"],d===le.Entered&&j["PopoverOverlay-open"],d===le.Exiting&&j["PopoverOverlay-exiting"],a==="cover"&&j["PopoverOverlay-noAnimation"]);return n.createElement(Qn,{ref:this.overlayRef,fullWidth:o,active:t,activator:r,preferInputActivator:s,preferredPosition:a,preferredAlignment:i,render:this.renderPopover.bind(this),fixed:l,onScrollOut:this.handleScrollOut,classNames:p,zIndexOverride:c})}clearTransitionTimeout(){this.enteringTimer&&window.clearTimeout(this.enteringTimer)}focusContent(){const{autofocusTarget:t="container"}=this.props;t==="none"||this.contentNode==null||requestAnimationFrame(()=>{if(this.contentNode.current==null)return;const r=io(this.contentNode.current);r&&t==="first-node"?r.focus({preventScroll:!1}):this.contentNode.current.focus({preventScroll:!1})})}}ar.contextType=In;function pa(e,t){const r=u.Children.toArray(e);return rr(r[0],Nt)?r:nr(r,Nt,t)}function mn(e,t){if(e===t)return!0;let r=t.parentNode;for(;r!=null;){if(r===e)return!0;r=r.parentNode}return!1}function fn(e,t){return t.current!=null&&e.includes(t.current)}function ma(e,t){return e.some(r=>r instanceof Node&&(t==null?void 0:t.contains(r)))}const fa=u.forwardRef(function({activatorWrapper:t="div",children:r,onClose:o,activator:a,preventFocusOnClose:i,active:s,fixed:l,ariaHaspopup:c,preferInputActivator:d=!0,zIndexOverride:p,...h},f){const[m,b]=u.useState(),P=u.useRef(null),E=u.useRef(null),I=t,S=u.useId();function y(){var g;(g=P.current)==null||g.forceUpdatePosition()}u.useImperativeHandle(f,()=>({forceUpdatePosition:y}));const A=u.useCallback(()=>{if(E.current==null)return;const v=$e(E.current)||E.current,T="disabled"in v&&!!v.disabled;la(v,{id:S,active:s,ariaHaspopup:c,activatorDisabled:T})},[S,s,c]),N=g=>{if(o(g),!(E.current==null||i)){if(g===Pe.FocusOut&&m){const v=$e(m)||$e(E.current)||E.current;rn(v,hn)||v.focus()}else if(g===Pe.EscapeKeypress&&m){const v=$e(m)||$e(E.current)||E.current;v?v.focus():rn(v,hn)}}};u.useEffect(()=>{(!m&&E.current||m&&E.current&&!E.current.contains(m))&&b(E.current.firstElementChild),A()},[m,A]),u.useEffect(()=>{m&&E.current&&b(E.current.firstElementChild),A()},[m,A]);const C=m?n.createElement(Gn,{idPrefix:"popover"},n.createElement(ar,Object.assign({ref:P,id:S,activator:m,preferInputActivator:d,onClose:N,active:s,fixed:l,zIndexOverride:p},h),r)):null;return n.createElement(I,{ref:E},u.Children.only(a),C)});function hn(e){let t=e.parentElement;for(;t;){if(t.matches(Xr.selector))return!1;t=t.parentElement}return!0}const ir=Object.assign(fa,{Pane:Nt,Section:or});function ha({accessibilityLabel:e,items:t=[],sections:r=[]}){const o=fe(),{value:a,toggle:i}=Ve(!1);if(t.length===0&&r.length===0)return null;const s=n.createElement("div",{className:sa.RollupActivator},n.createElement(Ne,{icon:Mn,accessibilityLabel:e||o.translate("Polaris.ActionMenu.RollupActions.rollupButton"),onClick:i}));return n.createElement(ir,{active:a,activator:s,preferredAlignment:"right",onClose:i,hideOnPrint:!0},n.createElement(Rt,{items:t,sections:r,onActionAnyItem:i}))}var st={ActionsLayoutOuter:"Polaris-ActionMenu-Actions__ActionsLayoutOuter",ActionsLayout:"Polaris-ActionMenu-Actions__ActionsLayout","ActionsLayout--measuring":"Polaris-ActionMenu-Actions--actionsLayoutMeasuring",ActionsLayoutMeasurer:"Polaris-ActionMenu-Actions__ActionsLayoutMeasurer"};function gn(e=[],t=[],r,o,a){const i=o.reduce((f,m)=>f+m,0),s=e.map((f,m)=>m),l=t.map((f,m)=>m),c=[],d=[],p=[],h=[];if(a>i)c.push(...s),p.push(...l);else{let f=0;s.forEach(m=>{const b=o[m];if(f+b>=a-r){d.push(m);return}c.push(m),f+=b}),l.forEach(m=>{const b=o[m+e.length];if(f+b>=a-r){h.push(m);return}p.push(m),f+=b})}return{visibleActions:c,hiddenActions:d,visibleGroups:p,hiddenGroups:h}}var ga={Details:"Polaris-ActionMenu-MenuGroup__Details"},bn={SecondaryAction:"Polaris-ActionMenu-SecondaryAction",critical:"Polaris-ActionMenu-SecondaryAction--critical"};function je({children:e,tone:t,helpText:r,onAction:o,destructive:a,...i}){const s=n.createElement(Ne,Object.assign({onClick:o,tone:a?"critical":void 0},i),e),l=r?n.createElement(qe,{preferredPosition:"below",content:r},s):s;return n.createElement("div",{className:w(bn.SecondaryAction,t==="critical"&&bn.critical)},l)}function vn({accessibilityLabel:e,active:t,actions:r,details:o,title:a,icon:i,disabled:s,onClick:l,onClose:c,onOpen:d,sections:p}){const h=u.useCallback(()=>{c(a)},[c,a]),f=u.useCallback(()=>{d(a)},[d,a]),m=u.useCallback(()=>{l?l(f):f()},[l,f]),b=n.createElement(je,{disclosure:!0,disabled:s,icon:i,accessibilityLabel:e,onClick:m},a);return n.createElement(ir,{active:!!t,activator:b,preferredAlignment:"left",onClose:h,hideOnPrint:!0},n.createElement(Rt,{items:r,sections:p,onActionAnyItem:h}),o&&n.createElement("div",{className:ga.Details},o))}const ba=8;function va({actions:e=[],groups:t=[],handleMeasurement:r}){const o=fe(),a=u.useRef(null),i={title:o.translate("Polaris.ActionMenu.Actions.moreActions")},s=n.createElement(je,{disclosure:!0},i.title),l=u.useCallback(()=>{if(!a.current)return;const p=a.current.offsetWidth,h=a.current.children,m=Array.from(h).map(P=>Math.ceil(P.getBoundingClientRect().width)+ba),b=m.pop()||0;r({containerWidth:p,disclosureWidth:b,hiddenActionsWidths:m})},[r]);u.useEffect(()=>{l()},[l,e,t]);const c=e.map(p=>{const{content:h,onAction:f,...m}=p;return n.createElement(je,Object.assign({key:h,onClick:f},m),h)}),d=t.map(p=>{const{title:h,icon:f}=p;return n.createElement(je,{key:h,disclosure:!0,icon:f},h)});return yn("resize",l),n.createElement("div",{className:st.ActionsLayoutMeasurer,ref:a},c,d,s)}function Pa({actions:e,groups:t,onActionRollup:r}){const o=fe(),a=u.useRef(null),[i,s]=u.useState(void 0),[l,c]=u.useReducer((B,L)=>({...B,...L}),{disclosureWidth:0,containerWidth:1/0,actionsWidths:[],visibleActions:[],hiddenActions:[],visibleGroups:[],hiddenGroups:[],hasMeasured:!1}),{visibleActions:d,hiddenActions:p,visibleGroups:h,hiddenGroups:f,containerWidth:m,disclosureWidth:b,actionsWidths:P,hasMeasured:E}=l,I={title:o.translate("Polaris.ActionMenu.Actions.moreActions"),actions:[]},S=u.useCallback(B=>s(i?void 0:B),[i]),y=u.useCallback(()=>s(void 0),[]);u.useEffect(()=>{if(m===0)return;const{visibleActions:B,visibleGroups:L,hiddenActions:$,hiddenGroups:O}=gn(e,t,b,P,m);c({visibleActions:B,visibleGroups:L,hiddenActions:$,hiddenGroups:O,hasMeasured:m!==1/0})},[m,b,e,t,P,c]);const A=u.useMemo(()=>e??[],[e]),N=u.useMemo(()=>t??[],[t]),C=A.filter((B,L)=>!!d.includes(L)).map(B=>{const{content:L,onAction:$,...O}=B;return n.createElement(je,Object.assign({key:L,onClick:$},O),L)}),v=(f.length>0||p.length>0?[...N,I]:[...N]).filter((B,L)=>{const $=N.length===0,O=h.includes(L),Y=B===I;return $?p.length>0:Y?!0:O}),T=p.map(B=>A[B]).filter(B=>B!=null),M=f.map(B=>N[B]).filter(B=>B!=null),H=v.map(B=>{const{title:L,actions:$,...O}=B,Y=B===I,Q=[...T,...M],[oe,U]=Q.reduce(([J,ae],K)=>(Ea(K)?ae.push({title:K.title,items:K.actions.map(ce=>({...ce,disabled:K.disabled||ce.disabled}))}):J.push(K),[J,ae]),[[],[]]);return Y?n.createElement(vn,Object.assign({key:L,title:L,active:L===i,actions:[...oe,...$],sections:U},O,{onOpen:S,onClose:y})):n.createElement(vn,Object.assign({key:L,title:L,active:L===i,actions:$},O,{onOpen:S,onClose:y}))}),F=u.useCallback(B=>{const{hiddenActionsWidths:L,containerWidth:$,disclosureWidth:O}=B,{visibleActions:Y,hiddenActions:Q,visibleGroups:oe,hiddenGroups:U}=gn(A,N,O,L,$);if(r){const J=Q.length>0||U.length>0;a.current!==J&&(r(J),a.current=J)}c({visibleActions:Y,hiddenActions:Q,visibleGroups:oe,hiddenGroups:U,actionsWidths:L,containerWidth:$,disclosureWidth:O,hasMeasured:!0})},[A,N,r]),R=n.createElement(va,{actions:e,groups:t,handleMeasurement:F});return n.createElement("div",{className:st.ActionsLayoutOuter},R,n.createElement("div",{className:w(st.ActionsLayout,!E&&st["ActionsLayout--measuring"])},C,H))}function Ea(e){return"title"in e}function xa({actions:e=[],groups:t=[],rollup:r,rollupActionsLabel:o,onActionRollup:a}){if(e.length===0&&t.length===0)return null;const i=w(pn.ActionMenu,r&&pn.rollup),s=t.map(l=>Ca(l));return n.createElement("div",{className:i},r?n.createElement(ha,{accessibilityLabel:o,items:e,sections:s}):n.createElement(Pa,{actions:e,groups:t,onActionRollup:a}))}function Sa(e=[]){return e.length===0?!1:e.some(t=>t.actions.length>0)}function Ca({title:e,actions:t,disabled:r}){return{title:e,items:t.map(o=>({...o,disabled:r||o.disabled}))}}var Se={ButtonGroup:"Polaris-ButtonGroup",Item:"Polaris-ButtonGroup__Item","Item-plain":"Polaris-ButtonGroup__Item--plain",variantSegmented:"Polaris-ButtonGroup--variantSegmented","Item-focused":"Polaris-ButtonGroup__Item--focused",fullWidth:"Polaris-ButtonGroup--fullWidth",extraTight:"Polaris-ButtonGroup--extraTight",tight:"Polaris-ButtonGroup--tight",loose:"Polaris-ButtonGroup--loose",noWrap:"Polaris-ButtonGroup--noWrap"};function Ia({button:e}){const{value:t,setTrue:r,setFalse:o}=Ve(!1),a=w(Se.Item,t&&Se["Item-focused"],e.props.variant==="plain"&&Se["Item-plain"]);return n.createElement("div",{className:a,onFocus:r,onBlur:o},e)}function Ta({children:e,gap:t,variant:r,fullWidth:o,connectedTop:a,noWrap:i}){const s=w(Se.ButtonGroup,t&&Se[t],r&&Se[X("variant",r)],o&&Se.fullWidth,i&&Se.noWrap),l=ua(e).map((c,d)=>n.createElement(Ia,{button:c,key:d}));return n.createElement("div",{className:s,"data-buttongroup-variant":r,"data-buttongroup-connected-top":a,"data-buttongroup-full-width":o,"data-buttongroup-no-wrap":i},l)}var Aa={Bleed:"Polaris-Bleed"};const wa=({marginInline:e,marginBlock:t,marginBlockStart:r,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:i,children:s})=>{const l=m=>{const b=["marginInlineStart","marginInlineEnd"],P=["marginBlockStart","marginBlockEnd"],E={marginBlockStart:r,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:i,marginInline:e,marginBlock:t};if(E[m])return E[m];if(b.includes(m)&&e)return E.marginInline;if(P.includes(m)&&t)return E.marginBlock},c=l("marginBlockStart"),d=l("marginBlockEnd"),p=l("marginInlineStart"),h=l("marginInlineEnd"),f={...be("bleed","margin-block-start","space",c),...be("bleed","margin-block-end","space",d),...be("bleed","margin-inline-start","space",p),...be("bleed","margin-inline-end","space",h)};return n.createElement("div",{className:Aa.Bleed,style:Mt(f)},s)};function ya({backAction:e}){const{content:t}=e;return n.createElement(Ne,{key:t,url:"url"in e?e.url:void 0,onClick:"onAction"in e?e.onAction:void 0,onPointerDown:ut,icon:Bn,accessibilityLabel:e.accessibilityLabel??t})}var ye;(function(e){e.Input="INPUT",e.Textarea="TEXTAREA",e.Select="SELECT",e.ContentEditable="contenteditable"})(ye||(ye={}));function Na(){if(document==null||document.activeElement==null)return!1;const{tagName:e}=document.activeElement;return e===ye.Input||e===ye.Textarea||e===ye.Select||document.activeElement.hasAttribute(ye.ContentEditable)}var nt={Pagination:"Polaris-Pagination",table:"Polaris-Pagination--table",TablePaginationActions:"Polaris-Pagination__TablePaginationActions"};function Ba({hasNext:e,hasPrevious:t,nextURL:r,previousURL:o,onNext:a,onPrevious:i,nextTooltip:s,previousTooltip:l,nextKeys:c,previousKeys:d,accessibilityLabel:p,accessibilityLabels:h,label:f,type:m="page"}){const b=fe(),P=u.createRef(),E=p||b.translate("Polaris.Pagination.pagination"),I=(h==null?void 0:h.previous)||b.translate("Polaris.Pagination.previous"),S=(h==null?void 0:h.next)||b.translate("Polaris.Pagination.next"),y=n.createElement(Ne,{icon:kn,accessibilityLabel:I,url:o,onClick:i,disabled:!t,id:"previousURL"}),A=l&&t?n.createElement(qe,{activatorWrapper:"span",content:l,preferredPosition:"below"},y):y,N=n.createElement(Ne,{icon:_n,accessibilityLabel:S,url:r,onClick:a,disabled:!e,id:"nextURL"}),C=s&&e?n.createElement(qe,{activatorWrapper:"span",content:s,preferredPosition:"below"},N):N,g=i||En,v=d&&(o||i)&&t&&d.map(R=>n.createElement(Xe,{key:R,keyCode:R,handler:rt(o?Pn("previousURL",P):g)})),T=a||En,M=c&&(r||a)&&e&&c.map(R=>n.createElement(Xe,{key:R,keyCode:R,handler:rt(r?Pn("nextURL",P):T)}));if(m==="table"){const R=f?n.createElement(z,{padding:"300",paddingBlockStart:"0",paddingBlockEnd:"0"},n.createElement(W,{as:"span",variant:"bodySm",fontWeight:"medium"},f)):null;return n.createElement("nav",{"aria-label":E,ref:P,className:w(nt.Pagination,nt.table)},v,M,n.createElement(z,{background:"bg-surface-secondary",paddingBlockStart:"150",paddingBlockEnd:"150",paddingInlineStart:"300",paddingInlineEnd:"200"},n.createElement(Ze,{align:"center",blockAlign:"center"},n.createElement("div",{className:nt.TablePaginationActions,"data-buttongroup-variant":"segmented"},n.createElement("div",null,A),R,n.createElement("div",null,C)))))}const H=e&&t?n.createElement("span",null,f):n.createElement(W,{tone:"subdued",as:"span"},f),F=f?n.createElement(z,{padding:"300",paddingBlockStart:"0",paddingBlockEnd:"0"},n.createElement("div",{"aria-live":"polite"},H)):null;return n.createElement("nav",{"aria-label":E,ref:P,className:nt.Pagination},v,M,n.createElement(Ta,{variant:"segmented"},A,F,C))}function Pn(e,t){return()=>{if(t.current==null)return;const r=t.current.querySelector(`#${e}`);r&&r.click()}}function rt(e){return()=>{Na()||e()}}function En(){}function sr(){const e=u.useContext(Yr);if(!e)throw new Error("No mediaQuery was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function Ue(e){return!u.isValidElement(e)&&e!==void 0}function lt(e){return u.isValidElement(e)&&e!==void 0}var ot={Page:"Polaris-Page",fullWidth:"Polaris-Page--fullWidth",narrowWidth:"Polaris-Page--narrowWidth",Content:"Polaris-Page__Content"},Z={TitleWrapper:"Polaris-Page-Header__TitleWrapper",TitleWrapperExpand:"Polaris-Page-Header__TitleWrapperExpand",BreadcrumbWrapper:"Polaris-Page-Header__BreadcrumbWrapper",PaginationWrapper:"Polaris-Page-Header__PaginationWrapper",PrimaryActionWrapper:"Polaris-Page-Header__PrimaryActionWrapper",Row:"Polaris-Page-Header__Row",mobileView:"Polaris-Page-Header--mobileView",RightAlign:"Polaris-Page-Header__RightAlign",noBreadcrumbs:"Polaris-Page-Header--noBreadcrumbs",AdditionalMetaData:"Polaris-Page-Header__AdditionalMetaData",Actions:"Polaris-Page-Header__Actions",longTitle:"Polaris-Page-Header--longTitle",mediumTitle:"Polaris-Page-Header--mediumTitle",isSingleRow:"Polaris-Page-Header--isSingleRow"},we={Title:"Polaris-Header-Title",TitleWithSubtitle:"Polaris-Header-Title__TitleWithSubtitle",TitleWrapper:"Polaris-Header-Title__TitleWrapper",SubTitle:"Polaris-Header-Title__SubTitle",SubtitleCompact:"Polaris-Header-Title__SubtitleCompact",SubtitleMaxWidth:"Polaris-Header-Title__SubtitleMaxWidth"};function ka({title:e,subtitle:t,titleMetadata:r,compactTitle:o,hasSubtitleMaxWidth:a}){const i=w(we.Title,t&&we.TitleWithSubtitle),s=e?n.createElement("h1",{className:i},n.createElement(W,{as:"span",variant:"headingLg",fontWeight:"bold"},e)):null,l=r?n.createElement(wa,{marginBlock:"100"},r):null,c=n.createElement("div",{className:we.TitleWrapper},s,l),d=t?n.createElement("div",{className:w(we.SubTitle,o&&we.SubtitleCompact,a&&we.SubtitleMaxWidth)},n.createElement(W,{as:"p",variant:"bodySm",tone:"subdued"},t)):null;return n.createElement(n.Fragment,null,c,d)}const _a=20,Ma=8,xn=34;function La({title:e,subtitle:t,pageReadyAccessibilityLabel:r,titleMetadata:o,additionalMetadata:a,titleHidden:i=!1,primaryAction:s,pagination:l,filterActions:c,backAction:d,secondaryActions:p=[],actionGroups:h=[],compactTitle:f=!1,onActionRollup:m}){const b=fe(),{isNavigationCollapsed:P}=sr(),E=!s&&!l&&(Ue(p)&&!p.length||lt(p))&&!h.length,I=h.length>0||Ue(p)&&p.length>0||lt(p),S=d?n.createElement("div",{className:Z.BreadcrumbWrapper},n.createElement(z,{maxWidth:"100%",paddingInlineEnd:"100",printHidden:!0},n.createElement(ya,{backAction:d}))):null,y=l&&!P?n.createElement("div",{className:Z.PaginationWrapper},n.createElement(z,{printHidden:!0},n.createElement(Ba,Object.assign({},l,{hasPrevious:l.hasPrevious,hasNext:l.hasNext})))):null,A=n.createElement("div",{className:w(Z.TitleWrapper,!I&&Z.TitleWrapperExpand)},n.createElement(ka,{title:e,subtitle:t,titleMetadata:o,compactTitle:f,hasSubtitleMaxWidth:I})),N=r||e,C=N?n.createElement("div",{role:"status"},n.createElement(W,{visuallyHidden:!0,as:"p"},b.translate("Polaris.Page.Header.pageReadyAccessibilityLabel",{title:N}))):void 0,g=s?n.createElement(Fa,{primaryAction:s}):null;let v=null;Ue(p)&&(p.length>0||Sa(h))?v=n.createElement(xa,{actions:p,groups:h,rollup:P,rollupActionsLabel:e?b.translate("Polaris.Page.Header.rollupActionsLabel",{title:e}):void 0,onActionRollup:m}):lt(p)&&(v=n.createElement(n.Fragment,null,p));const T=S||y?n.createElement(z,{printHidden:!0,paddingBlockEnd:"100",paddingInlineEnd:v&&P?"1000":void 0},n.createElement(Ze,{gap:"400",align:"space-between",blockAlign:"center"},S,y)):null,M=a?n.createElement("div",{className:Z.AdditionalMetaData},n.createElement(W,{tone:"subdued",as:"span",variant:"bodySm"},a)):null,H=w(E&&Z.isSingleRow,T&&Z.hasNavigation,v&&Z.hasActionMenu,P&&Z.mobileView,!d&&Z.noBreadcrumbs,e&&e.length<xn&&Z.mediumTitle,e&&e.length>xn&&Z.longTitle),{slot1:F,slot2:R,slot3:B,slot4:L,slot5:$}=Ra({actionMenuMarkup:v,additionalMetadataMarkup:M,breadcrumbMarkup:S,isNavigationCollapsed:P,pageTitleMarkup:A,paginationMarkup:y,primaryActionMarkup:g,title:e});return n.createElement(z,{position:"relative",paddingBlockStart:{xs:"400",md:"600"},paddingBlockEnd:{xs:"400",md:"600"},paddingInlineStart:{xs:"400",sm:"0"},paddingInlineEnd:{xs:"400",sm:"0"},visuallyHidden:i},C,n.createElement("div",{className:H},n.createElement(Eo,{filterActions:!!c},n.createElement(wt,{condition:[F,R,B,L].some(at)},n.createElement("div",{className:Z.Row},F,R,n.createElement(wt,{condition:[B,L].some(at)},n.createElement("div",{className:Z.RightAlign},n.createElement(da,{condition:[B,L].every(at),wrapper:O=>n.createElement("div",{className:Z.Actions},O)},B,L))))),n.createElement(wt,{condition:[$].some(at)},n.createElement("div",{className:Z.Row},n.createElement(Ze,{gap:"400"},$))))))}function Fa({primaryAction:e}){const{isNavigationCollapsed:t}=sr();let r;if(Ue(e)){const{primary:o,helpText:a}=e,i=o===void 0?!0:o,s=Dn(Oa(t,e),{variant:i?"primary":void 0});r=a?n.createElement(qe,{content:a},s):s}else r=e;return n.createElement("div",{className:Z.PrimaryActionWrapper},n.createElement(z,{printHidden:!0},r))}function Oa(e,t){let{content:r,accessibilityLabel:o}=t;const{icon:a}=t;return a==null?{...t,icon:void 0}:(e&&(o=o||r,r=void 0),{...t,content:r,accessibilityLabel:o,icon:a})}function at(e){return e!=null}function Ra({actionMenuMarkup:e,additionalMetadataMarkup:t,breadcrumbMarkup:r,isNavigationCollapsed:o,pageTitleMarkup:a,paginationMarkup:i,primaryActionMarkup:s,title:l}){const c={mobileCompact:{slots:{slot1:null,slot2:a,slot3:e,slot4:s,slot5:t},condition:o&&r==null&&l!=null&&l.length<=Ma},mobileDefault:{slots:{slot1:r,slot2:a,slot3:e,slot4:s,slot5:t},condition:o},desktopCompact:{slots:{slot1:r,slot2:a,slot3:e,slot4:s,slot5:t},condition:!o&&i==null&&e==null&&l!=null&&l.length<=_a},desktopDefault:{slots:{slot1:r,slot2:a,slot3:n.createElement(n.Fragment,null,e,s),slot4:i,slot5:t},condition:!o}};return(Object.values(c).find(p=>p.condition)||c.desktopDefault).slots}function za({children:e,fullWidth:t,narrowWidth:r,...o}){const a=w(ot.Page,t&&ot.fullWidth,r&&ot.narrowWidth),i=o.title!=null&&o.title!==""||o.subtitle!=null&&o.subtitle!==""||o.primaryAction!=null||o.secondaryActions!=null&&(Ue(o.secondaryActions)&&o.secondaryActions.length>0||lt(o.secondaryActions))||o.actionGroups!=null&&o.actionGroups.length>0||o.backAction!=null,s=w(!i&&ot.Content),l=i?n.createElement(La,Object.assign({filterActions:!0},o)):null;return n.createElement("div",{className:a},l,n.createElement("div",{className:s},e))}export{Po as B,Da as C,Ze as I,za as P,$n as S,W as T,Hn as U,to as W,z as a,ra as b,w as c,Ne as d,me as e,Ta as f,yn as g,Un as h,rr as i,fe as u,X as v,nr as w};
