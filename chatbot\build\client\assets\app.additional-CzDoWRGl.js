import{R as n,j as e}from"./index-C0U6NBub.js";import{c as B,U as L,P as C,C as m,B as u,T as r,a as T}from"./Page-BAvRnlvb.js";import{B as U,T as w,L as i,a as x}from"./TitleBar-DiPv-53d.js";import"./context-BJzNQyky.js";var s={Link:"Polaris-Link",monochrome:"Polaris-Link--monochrome",removeUnderline:"Polaris-Link--removeUnderline"};function g({url:a,children:o,onClick:d,external:j,target:v,id:l,monochrome:k,removeUnderline:b,accessibilityLabel:p,dataPrimaryLink:c}){return n.createElement(U.Consumer,null,f=>{const y=k||f,h=B(s.Link,y&&s.monochrome,b&&s.removeUnderline);return a?n.createElement(L,{onClick:d,className:h,url:a,external:j,target:v,id:l,"aria-label":p,"data-primary-link":c},o):n.createElement("button",{type:"button",onClick:d,className:h,id:l,"aria-label":p,"data-primary-link":c},o)})}function R(){return e.jsxs(C,{children:[e.jsx(w,{title:"Additional page"}),e.jsxs(i,{children:[e.jsx(i.Section,{children:e.jsx(m,{children:e.jsxs(u,{gap:"300",children:[e.jsxs(r,{as:"p",variant:"bodyMd",children:["The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using"," ",e.jsx(g,{url:"https://shopify.dev/docs/apps/tools/app-bridge",target:"_blank",removeUnderline:!0,children:"App Bridge"}),"."]}),e.jsxs(r,{as:"p",variant:"bodyMd",children:["To create your own page and have it show up in the app navigation, add a page inside ",e.jsx(t,{children:"app/routes"}),", and a link to it in the ",e.jsx(t,{children:"<NavMenu>"})," component found in ",e.jsx(t,{children:"app/routes/app.jsx"}),"."]})]})})}),e.jsx(i.Section,{variant:"oneThird",children:e.jsx(m,{children:e.jsxs(u,{gap:"200",children:[e.jsx(r,{as:"h2",variant:"headingMd",children:"Resources"}),e.jsx(x,{children:e.jsx(x.Item,{children:e.jsx(g,{url:"https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",target:"_blank",removeUnderline:!0,children:"App nav best practices"})})})]})})})]})]})}function t({children:a}){return e.jsx(T,{as:"span",padding:"025",paddingInlineStart:"100",paddingInlineEnd:"100",background:"bg-surface-active",borderWidth:"025",borderColor:"border",borderRadius:"100",children:e.jsx("code",{children:a})})}export{R as default};
