import{r as y,R as t}from"./index-C0U6NBub.js";import{c as l,v as c,T as m,a as _}from"./Page-BAvRnlvb.js";const A=y.createContext(!1);var s={Layout:"Polaris-Layout",Section:"Polaris-Layout__Section","Section-fullWidth":"Polaris-Layout__Section--fullWidth","Section-oneHalf":"Polaris-Layout__Section--oneHalf","Section-oneThird":"Polaris-Layout__Section--oneThird",AnnotatedSection:"Polaris-Layout__AnnotatedSection",AnnotationWrapper:"Polaris-Layout__AnnotationWrapper",AnnotationContent:"Polaris-Layout__AnnotationContent",Annotation:"Polaris-Layout__Annotation"},u={TextContainer:"Polaris-TextContainer",spacingTight:"Polaris-TextContainer--spacingTight",spacingLoose:"Polaris-TextContainer--spacingLoose"};function S({spacing:n,children:a}){const e=l(u.TextContainer,n&&u[c("spacing",n)]);return t.createElement("div",{className:e},a)}function E({children:n,title:a,description:e,id:o}){const i=typeof e=="string"?t.createElement(m,{as:"p",variant:"bodyMd"},e):e;return t.createElement("div",{className:s.AnnotatedSection},t.createElement("div",{className:s.AnnotationWrapper},t.createElement("div",{className:s.Annotation},t.createElement(S,{spacing:"tight"},t.createElement(m,{id:o,variant:"headingMd",as:"h2"},a),i&&t.createElement(_,{color:"text-secondary"},i))),t.createElement("div",{className:s.AnnotationContent},n)))}function L({children:n,variant:a}){const e=l(s.Section,s[`Section-${a}`]);return t.createElement("div",{className:e},n)}const d=function({sectioned:a,children:e}){const o=a?t.createElement(L,null,e):e;return t.createElement("div",{className:s.Layout},o)};d.AnnotatedSection=E;d.Section=L;var r={List:"Polaris-List",typeNumber:"Polaris-List--typeNumber",Item:"Polaris-List__Item",spacingLoose:"Polaris-List--spacingLoose"};function N({children:n}){return t.createElement("li",{className:r.Item},n)}const P=function({children:a,gap:e="loose",type:o="bullet"}){const i=l(r.List,e&&r[c("spacing",e)],o&&r[c("type",o)]),p=o==="bullet"?"ul":"ol";return t.createElement(p,{className:i},a)};P.Item=N;const T="ui-title-bar";export{A as B,d as L,T,P as a};
