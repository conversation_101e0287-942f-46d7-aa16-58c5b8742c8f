{"name": "@shopify/shopify-app-session-storage-memory", "version": "4.0.18", "description": "Shopify App Session Storage for Memory", "repository": {"type": "git", "url": "git+https://github.com/Shopify/shopify-app-js.git", "directory": "packages/apps/session-storage/shopify-app-session-storage-memory"}, "bugs": {"url": "https://github.com/Shopify/shopify-app-js/issues"}, "homepage": "https://github.com/Shopify/shopify-app-js/tree/main/packages/apps/session-storage/shopify-app-session-storage-memory", "author": "Shopify Inc.", "license": "MIT", "main": "./dist/cjs/memory.js", "module": "./dist/esm/memory.mjs", "types": "./dist/ts/memory.d.ts", "publishConfig": {"access": "public"}, "keywords": ["shopify", "node", "app", "graphql", "rest", "webhook", "Admin API", "Storefront API", "session storage"], "dependencies": {}, "peerDependencies": {"@shopify/shopify-api": "^11.0.0", "@shopify/shopify-app-session-storage": "^3.0.0"}, "devDependencies": {"@shopify/shopify-app-session-storage-test-utils": "^3.0.18"}, "files": ["dist/*", "!tsconfig.tsbuildinfo", "!node_modules"], "scripts": {"lint": "eslint . --ext .js,.ts", "build": "pnpm rollup && pnpm tsc -p ./tsconfig.build.json", "tsc": "tsc", "test": "jest", "test:ci": "pnpm test", "rollup": "rollup -c rollup.config.js --bundleConfigAsCjs", "clean": "rimraf .rollup.cache dist", "changeset": "changeset", "version": "changeset version", "release": "pnpm build && changeset publish"}}